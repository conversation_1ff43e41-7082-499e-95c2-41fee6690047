{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"SerilogWebApp/1.0.0": {"dependencies": {"Logger": "1.0.0", "Serilog.AspNetCore": "5.0.0", "Serilog.Enrichers.Environment": "2.2.0", "Serilog.Enrichers.Process": "2.0.2", "Serilog.Enrichers.Thread": "3.1.0", "Serilog.Formatting.Syslog": "1.0.1", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"SerilogWebApp.dll": {}}}, "Microsoft.Extensions.Configuration/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8"}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}}, "Microsoft.Extensions.DependencyInjection/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {}, "Microsoft.Extensions.DependencyModel/3.0.0": {"dependencies": {"System.Text.Json": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.19.46305"}}}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {}, "Microsoft.Extensions.Options/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.Extensions.Primitives/5.0.0": {}, "Serilog/2.11.0": {"runtime": {"lib/net5.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Serilog.AspNetCore/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0", "Serilog": "2.11.0", "Serilog.Extensions.Hosting": "4.2.0", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.3.0", "Serilog.Sinks.Console": "4.0.1", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Environment/2.2.0": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.Environment.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Process/2.0.2": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.Process.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Thread/3.1.0": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/4.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Hosting.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Serilog": "2.11.0", "Serilog.Extensions.Logging": "3.1.0"}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "5.0.0", "Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Syslog/1.0.1": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Syslog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/3.3.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "3.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1", "Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "Serilog.Sinks.Console/4.0.1": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.0.1.0"}}}, "Serilog.Sinks.Datadog.Logs/0.5.6": {"dependencies": {"Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.1", "Serilog": "2.11.0", "Serilog.Sinks.PeriodicBatching": "3.1.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Datadog.Logs.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SerilogTimings/2.3.0": {"dependencies": {"Serilog": "2.11.0"}, "runtime": {"lib/netstandard2.0/SerilogTimings.dll": {"assemblyVersion": "2.3.0.0", "fileVersion": "2.3.0.0"}}}, "System.Text.Json/4.6.0": {}, "Logger/1.0.0": {"dependencies": {"Serilog": "2.11.0", "Serilog.AspNetCore": "5.0.0", "Serilog.Enrichers.Environment": "2.2.0", "Serilog.Enrichers.Process": "2.0.2", "Serilog.Enrichers.Thread": "3.1.0", "Serilog.Extensions.Logging": "3.1.0", "Serilog.Sinks.Datadog.Logs": "0.5.6", "Serilog.Sinks.File": "5.0.0", "SerilogTimings": "2.3.0"}, "runtime": {"Logger.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"SerilogWebApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LjVKO6P2y52c5ZhTLX/w8zc5H4Y3J/LJsgqTBj49TtFq/hAtVNue/WA0F6/7GMY90xhD7K0MDZ4qpOeWXbLvzg==", "path": "microsoft.extensions.configuration/2.1.1", "hashPath": "microsoft.extensions.configuration.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-0qbNyxGpuNP/fuQ3FLHesm1Vn/83qYcAgVsi1UQCQN1peY4YH1uiizOh4xbYkQyxiVMD/c/zhiYYv94G0DXSSA==", "path": "microsoft.extensions.configuration.abstractions/3.1.8", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-fcLCTS03poWE4v9tSNBr3pWn0QwGgAn1vzqHXlXgvqZeOc7LvQNzaWcKRQZTdEc3+YhQKwMsOtm3VKSA2aWQ8w==", "path": "microsoft.extensions.configuration.binder/2.1.1", "hashPath": "microsoft.extensions.configuration.binder.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rc2kb/p3Ze6cP6rhFC3PJRdWGbLvSHZc0ev7YlyeU6FmHciDMLrhoVoTUEzKPhN5ZjFgKF1Cf5fOz8mCMIkvpA==", "path": "microsoft.extensions.dependencyinjection/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Iaectmzg9Dc4ZbKX/FurrRjgO/I8rTumL5UU+Uube6vZuGetcnXoIgTA94RthFWePhdMVm8MMhVFJZdbzMsdyQ==", "path": "microsoft.extensions.dependencymodel/3.0.0", "hashPath": "microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-U7ffyzrPfRDH5K3h/mBpqJVoHbppw1kc1KyHZcZeDR7b1A0FRaqMSiizGpN9IGwWs9BuN7oXIKFyviuSGBjHtQ==", "path": "microsoft.extensions.fileproviders.abstractions/3.1.8", "hashPath": "microsoft.extensions.fileproviders.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-7ZJUKwPipkDvuv2KJPZ3r01wp2AWNMiYH+61i0dL89F7QICknjKpWgLKLpTSUYFgl77S3b4264I6i4HzDdrb2A==", "path": "microsoft.extensions.hosting.abstractions/3.1.8", "hashPath": "microsoft.extensions.hosting.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "path": "microsoft.extensions.logging/5.0.0", "hashPath": "microsoft.extensions.logging.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "path": "microsoft.extensions.logging.abstractions/5.0.0", "hashPath": "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "path": "microsoft.extensions.options/5.0.0", "hashPath": "microsoft.extensions.options.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-NpGh3Y/VOBs6hvjKHMsdbtrvGvMO+cBqZ7YT/Rc4iFy0C4ogSnl1lBAq69L1LS6gzlwDBZDZ7WcvzSDzk5zfzA==", "path": "microsoft.extensions.options.configurationextensions/2.1.1", "hashPath": "microsoft.extensions.options.configurationextensions.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "Serilog/2.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-ysv+hBzTul6Dp+Hvm10FlhJO3yMQcFKSAleus+LpiIzvNstpeV4Z7gGuIZ1OPNfIMulSHOjmLuGAEDKzpnV8ZQ==", "path": "serilog/2.11.0", "hashPath": "serilog.2.11.0.nupkg.sha512"}, "Serilog.AspNetCore/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/JO/txIxRR61x1UXQAgUzG2Sx05o1QHCkokVBWrKzmAoDu+p5EtCAj7L/TVVg7Ezhh3GPiZ0JI9OJCmRO9tSRw==", "path": "serilog.aspnetcore/5.0.0", "hashPath": "serilog.aspnetcore.5.0.0.nupkg.sha512"}, "Serilog.Enrichers.Environment/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-DMrj3A4l65kc4JouQyZjjFv7N58Y7lGsB81kSzorTwUGeI2wrTy7CVwSOfG90/Pcu/HV5bwGrUmxDZ38pON+5Q==", "path": "serilog.enrichers.environment/2.2.0", "hashPath": "serilog.enrichers.environment.2.2.0.nupkg.sha512"}, "Serilog.Enrichers.Process/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-T9EjKKLsL6qC/3eOLUAKEPBLEqPDmt5BLXaQdPMaxJzuex+MeXA8DuAiPboUaftp3kbnCN4ZgZpDvs+Fa7OHuw==", "path": "serilog.enrichers.process/2.0.2", "hashPath": "serilog.enrichers.process.2.0.2.nupkg.sha512"}, "Serilog.Enrichers.Thread/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-85lWsGRJpRxvKT6j/H67no55SUBsBIvp556TKuBTGhjtoPeq+L7j/sDWbgAtvT0p7u7/phJyX6j35PQ4Vtqw0g==", "path": "serilog.enrichers.thread/3.1.0", "hashPath": "serilog.enrichers.thread.3.1.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gT2keceCmPQR9EX0VpXQZvUgELdfE7yqJ7MOxBhm3WLCblcvRgswEOOTgok/DHObbM15A3V/DtF3VdVDQPIZzQ==", "path": "serilog.extensions.hosting/4.2.0", "hashPath": "serilog.extensions.hosting.4.2.0.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Formatting.Syslog/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-T0MsJmZCH+YA9jlPlEEH75RocjN12mF4c3T/2l6kAddnp/tI0ZFc9RKOINJgl2rwcaUGN4GWvlDygLtlc2M9fw==", "path": "serilog.formatting.syslog/1.0.1", "hashPath": "serilog.formatting.syslog.1.0.1.nupkg.sha512"}, "Serilog.Settings.Configuration/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7GNudISZwqaT902hqEL2OFGTZeUFWfnrNLupJkOqeF41AR3GjcxX+Hwb30xb8gG2/CDXsCMVfF8o0+8KY0fJNg==", "path": "serilog.settings.configuration/3.3.0", "hashPath": "serilog.settings.configuration.3.3.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-apLOvSJQLlIbKlbx+Y2UDHSP05kJsV7mou+fvJoRGs/iR+jC22r8cuFVMjjfVxz/AD4B2UCltFhE1naRLXwKNw==", "path": "serilog.sinks.console/4.0.1", "hashPath": "serilog.sinks.console.4.0.1.nupkg.sha512"}, "Serilog.Sinks.Datadog.Logs/0.5.6": {"type": "package", "serviceable": true, "sha512": "sha512-gZkyS2+a4kMchbCntqiKvbq3wFm+L4yWT1OgA/yASPpg+AHPhd4sY9eb7nvIZFiTPKUqHDJkY1uZtQlaeR0lMg==", "path": "serilog.sinks.datadog.logs/0.5.6", "hashPath": "serilog.sinks.datadog.logs.0.5.6.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "path": "serilog.sinks.periodicbatching/3.1.0", "hashPath": "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512"}, "SerilogTimings/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RPMHt1RT06dngccPDut4OztOS3AGwGSJj6w4ri/xhaXsIVC3F6OZIqaC0KqkPR6L/0CT7QKkB5oCjPxwRrDesg==", "path": "serilogtimings/2.3.0", "hashPath": "serilogtimings.2.3.0.nupkg.sha512"}, "System.Text.Json/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-4F8Xe+JIkVoDJ8hDAZ7HqLkjctN/6WItJIzQaifBwClC7wmoLSda/Sv2i6i1kycqDb3hWF4JCVbpAweyOKHEUA==", "path": "system.text.json/4.6.0", "hashPath": "system.text.json.4.6.0.nupkg.sha512"}, "Logger/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}