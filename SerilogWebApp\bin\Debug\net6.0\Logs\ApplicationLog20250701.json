{"@t":"2025-07-01T11:28:37.8632612Z","@m":"User profile is available. Using '\"C:\\Users\\<USER>\\AppData\\Local\\ASP.NET\\DataProtection-Keys\"' as key repository and Windows DPAPI to encrypt keys at rest.","@i":"7ac5e29c","FullName":"C:\\Users\\<USER>\\AppData\\Local\\ASP.NET\\DataProtection-Keys","EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0001"}
{"@t":"2025-07-01T11:28:38.8398890Z","@m":"Application started. Press Ctrl+C to shut down.","@i":"dcaefe54","SourceContext":"Microsoft.Hosting.Lifetime","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0001"}
{"@t":"2025-07-01T11:28:38.8406306Z","@m":"Hosting environment: \"Development\"","@i":"c3307c92","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0001"}
{"@t":"2025-07-01T11:28:38.8407621Z","@m":"Content root path: \"C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\"","@i":"b5d60022","contentRoot":"C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp","SourceContext":"Microsoft.Hosting.Lifetime","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0001"}
{"@t":"2025-07-01T11:28:38.8477550Z","@m":"Request starting HTTP/2 GET https://localhost:44327/ - -","@i":"ca22a1cb","Protocol":"HTTP/2","Method":"GET","ContentType":null,"ContentLength":null,"Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/","QueryString":"","HostingRequestStartingLog":"Request starting HTTP/2 GET https://localhost:44327/ - -","EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","ThreadID":"0010"}
{"@t":"2025-07-01T11:28:39.3010865Z","@m":"Executing endpoint '\"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)\"'","@i":"500cc934","EndpointName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:39.4001684Z","@m":"Route matched with \"{action = \\\"Index\\\", controller = \\\"Home\\\"}\". Executing controller action with signature \"Microsoft.AspNetCore.Mvc.IActionResult Index()\" on controller \"SerilogWebApp.Controllers.HomeController\" (\"SerilogWebApp\").","@i":"122b2fdf","RouteData":"{action = \"Index\", controller = \"Home\"}","MethodInfo":"Microsoft.AspNetCore.Mvc.IActionResult Index()","Controller":"SerilogWebApp.Controllers.HomeController","AssemblyName":"SerilogWebApp","EventId":{"Id":3,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"0050a95d-923f-4abd-bc9f-c0fa7662a5a7","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:39.4126664Z","@m":"HomeController.cs Index 42 Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","@i":"52f3948c","ApplicationName":"SerilogWebApp","ActionId":"0050a95d-923f-4abd-bc9f-c0fa7662a5a7","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:39.4161020Z","@m":"HomeController.cs Index 43 System.Exception: Exception of type 'System.Exception' was thrown.","@i":"e82d03b1","@l":"Error","ApplicationName":"SerilogWebApp","ActionId":"0050a95d-923f-4abd-bc9f-c0fa7662a5a7","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:39.4165505Z","@m":"HomeController.cs Index 44 This is an information","@i":"bd205256","ApplicationName":"SerilogWebApp","ActionId":"0050a95d-923f-4abd-bc9f-c0fa7662a5a7","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:39.4366038Z","@m":"Executing ViewResult, running view \"Index\".","@i":"c83d0e25","ViewName":"Index","EventId":{"Id":1,"Name":"ViewResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor","ActionId":"0050a95d-923f-4abd-bc9f-c0fa7662a5a7","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:39.8227876Z","@m":"Executed ViewResult - view \"Index\" executed in 382.2302ms.","@i":"f66409e9","ViewName":"Index","ElapsedMilliseconds":382.2302,"EventId":{"Id":4,"Name":"ViewResultExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor","ActionId":"0050a95d-923f-4abd-bc9f-c0fa7662a5a7","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:39.8310303Z","@m":"Executed action \"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)\" in 429.1484ms","@i":"afa2e885","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","ElapsedMilliseconds":429.1484,"EventId":{"Id":2,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:39.8312268Z","@m":"Executed endpoint '\"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)\"'","@i":"99874f2b","EndpointName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:39.8421920Z","@m":"HTTP \"GET\" \"/\" responded 200 in 568.0149 ms","@i":"62d0885c","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","ActionId":"0050a95d-923f-4abd-bc9f-c0fa7662a5a7","RequestMethod":"GET","RequestPath":"/","StatusCode":200,"Elapsed":568.0149,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","ApplicationName":"SerilogWebApp","RequestId":"********-0007-f800-b63f-84710c7967bb","Host":"MAILPTP93","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:40.0488754Z","@m":"Request starting HTTP/2 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js - -","@i":"ca22a1cb","Protocol":"HTTP/2","Method":"GET","ContentType":null,"ContentLength":null,"Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/_framework/aspnetcore-browser-refresh.js","QueryString":"","HostingRequestStartingLog":"Request starting HTTP/2 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js - -","EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"400000db-0005-f500-b63f-84710c7967bb","RequestPath":"/_framework/aspnetcore-browser-refresh.js","ApplicationName":"SerilogWebApp","ThreadID":"0010"}
{"@t":"2025-07-01T11:28:40.1167315Z","@m":"Request finished HTTP/2 GET https://localhost:44327/ - - - 200 - text/html;+charset=utf-8 1275.7497ms","@i":"791a596a","ElapsedMilliseconds":1275.7497,"StatusCode":200,"ContentType":"text/html; charset=utf-8","ContentLength":null,"Protocol":"HTTP/2","Method":"GET","Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/","QueryString":"","HostingRequestFinishedLog":"Request finished HTTP/2 GET https://localhost:44327/ - - - 200 - text/html;+charset=utf-8 1275.7497ms","EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"********-0007-f800-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","ThreadID":"0008"}
{"@t":"2025-07-01T11:28:40.1251814Z","@m":"Request starting HTTP/2 GET https://localhost:44327/_vs/browserLink - -","@i":"ca22a1cb","Protocol":"HTTP/2","Method":"GET","ContentType":null,"ContentLength":null,"Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/_vs/browserLink","QueryString":"","HostingRequestStartingLog":"Request starting HTTP/2 GET https://localhost:44327/_vs/browserLink - -","EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"40000037-0007-f800-b63f-84710c7967bb","RequestPath":"/_vs/browserLink","ApplicationName":"SerilogWebApp","ThreadID":"0006"}
{"@t":"2025-07-01T11:28:40.1259120Z","@m":"Request finished HTTP/2 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js - - - 200 12358 application/javascript;+charset=utf-8 76.6520ms","@i":"791a596a","ElapsedMilliseconds":76.652,"StatusCode":200,"ContentType":"application/javascript; charset=utf-8","ContentLength":12358,"Protocol":"HTTP/2","Method":"GET","Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/_framework/aspnetcore-browser-refresh.js","QueryString":"","HostingRequestFinishedLog":"Request finished HTTP/2 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js - - - 200 12358 application/javascript;+charset=utf-8 76.6520ms","EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"400000db-0005-f500-b63f-84710c7967bb","RequestPath":"/_framework/aspnetcore-browser-refresh.js","ApplicationName":"SerilogWebApp","ThreadID":"0010"}
{"@t":"2025-07-01T11:28:40.5133116Z","@m":"Request finished HTTP/2 GET https://localhost:44327/_vs/browserLink - - - 200 - text/javascript;+charset=UTF-8 388.0284ms","@i":"791a596a","ElapsedMilliseconds":388.0284,"StatusCode":200,"ContentType":"text/javascript; charset=UTF-8","ContentLength":null,"Protocol":"HTTP/2","Method":"GET","Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/_vs/browserLink","QueryString":"","HostingRequestFinishedLog":"Request finished HTTP/2 GET https://localhost:44327/_vs/browserLink - - - 200 - text/javascript;+charset=UTF-8 388.0284ms","EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"40000037-0007-f800-b63f-84710c7967bb","RequestPath":"/_vs/browserLink","ApplicationName":"SerilogWebApp","ThreadID":"0009"}
