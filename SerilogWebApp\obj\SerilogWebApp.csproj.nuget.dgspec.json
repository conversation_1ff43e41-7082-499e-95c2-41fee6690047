{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\SerilogWebApp.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\Logger\\Logger.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\Logger\\Logger.csproj", "projectName": "<PERSON><PERSON>", "projectPath": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\Logger\\Logger.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\Logger\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.telerik.com/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Serilog": {"target": "Package", "version": "[2.11.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[2.2.0, )"}, "Serilog.Enrichers.Process": {"target": "Package", "version": "[2.0.2, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.Sinks.Datadog.Logs": {"target": "Package", "version": "[0.5.6, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "SerilogTimings": {"target": "Package", "version": "[2.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[3.1.10, 3.1.10]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[3.1.0, 3.1.0]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[3.1.0, 3.1.0]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\SerilogWebApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\SerilogWebApp.csproj", "projectName": "SerilogWebApp", "projectPath": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\SerilogWebApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.telerik.com/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\Logger\\Logger.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\Logger\\Logger.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Serilog.AspNetCore": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[2.2.0, )"}, "Serilog.Enrichers.Process": {"target": "Package", "version": "[2.0.2, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.Formatting.Syslog": {"target": "Package", "version": "[1.0.1, )"}, "Serilog.Sinks.Datadog.Logs": {"target": "Package", "version": "[0.5.6, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}