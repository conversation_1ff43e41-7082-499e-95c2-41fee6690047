D:\SerilogWebApp\Logger\bin\Debug\netcoreapp3.1\Logger.deps.json
D:\SerilogWebApp\Logger\bin\Debug\netcoreapp3.1\Logger.dll
D:\SerilogWebApp\Logger\bin\Debug\netcoreapp3.1\Logger.pdb
D:\SerilogWebApp\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.AssemblyReference.cache
D:\SerilogWebApp\Logger\obj\Debug\netcoreapp3.1\Logger.GeneratedMSBuildEditorConfig.editorconfig
D:\SerilogWebApp\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfoInputs.cache
D:\SerilogWebApp\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfo.cs
D:\SerilogWebApp\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.CoreCompileInputs.cache
D:\SerilogWebApp\Logger\obj\Debug\netcoreapp3.1\Logger.dll
D:\SerilogWebApp\Logger\obj\Debug\netcoreapp3.1\Logger.pdb
D:\SerilogWebApp - Copy\Logger\bin\Debug\netcoreapp3.1\Logger.deps.json
D:\SerilogWebApp - Copy\Logger\bin\Debug\netcoreapp3.1\Logger.dll
D:\SerilogWebApp - Copy\Logger\bin\Debug\netcoreapp3.1\Logger.pdb
D:\SerilogWebApp - Copy\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.AssemblyReference.cache
D:\SerilogWebApp - Copy\Logger\obj\Debug\netcoreapp3.1\Logger.GeneratedMSBuildEditorConfig.editorconfig
D:\SerilogWebApp - Copy\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfoInputs.cache
D:\SerilogWebApp - Copy\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfo.cs
D:\SerilogWebApp - Copy\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.CoreCompileInputs.cache
D:\SerilogWebApp - Copy\Logger\obj\Debug\netcoreapp3.1\Logger.dll
D:\SerilogWebApp - Copy\Logger\obj\Debug\netcoreapp3.1\Logger.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\bin\Debug\netcoreapp3.1\Logger.deps.json
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\bin\Debug\netcoreapp3.1\Logger.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\bin\Debug\netcoreapp3.1\Logger.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.AssemblyReference.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\obj\Debug\netcoreapp3.1\Logger.GeneratedMSBuildEditorConfig.editorconfig
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfoInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.CoreCompileInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\obj\Debug\netcoreapp3.1\Logger.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\Logger\obj\Debug\netcoreapp3.1\Logger.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\bin\Debug\netcoreapp3.1\Logger.deps.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\bin\Debug\netcoreapp3.1\Logger.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\bin\Debug\netcoreapp3.1\Logger.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.AssemblyReference.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.GeneratedMSBuildEditorConfig.editorconfig
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfoInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.CoreCompileInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.pdb
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\bin\Debug\netcoreapp3.1\Logger.deps.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\bin\Debug\netcoreapp3.1\Logger.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\bin\Debug\netcoreapp3.1\Logger.pdb
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.AssemblyReference.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfoInputs.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.AssemblyInfo.cs
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.csproj.CoreCompileInputs.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\Logger\obj\Debug\netcoreapp3.1\Logger.pdb
