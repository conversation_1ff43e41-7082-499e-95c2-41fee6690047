D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.Development.json
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.json
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.exe
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.deps.json
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.json
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.dev.json
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Binder.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyModel.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Hosting.Abstractions.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.AspNetCore.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Hosting.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Logging.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Compact.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Settings.Configuration.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Console.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Debug.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.File.dll
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.AssemblyReference.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.GeneratedMSBuildEditorConfig.editorconfig
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfoInputs.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfo.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CoreCompileInputs.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\SerilogWebApp.StaticWebAssets.Manifest.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.input.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.output.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorCoreGenerate.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Index.cshtml.g.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Privacy.cshtml.g.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\Error.cshtml.g.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_Layout.cshtml.g.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_ValidationScriptsPartial.cshtml.g.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewImports.cshtml.g.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewStart.cshtml.g.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cache
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cs
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CopyComplete
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\Backup\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.genruntimeconfig.cache
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Environment.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Syslog.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Thread.dll
D:\Backup\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Process.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.Development.json
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.json
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.exe
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.deps.json
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.json
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.dev.json
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Binder.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyModel.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Hosting.Abstractions.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.AspNetCore.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Environment.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Process.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Thread.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Hosting.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Logging.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Compact.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Syslog.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Settings.Configuration.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Console.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Debug.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.File.dll
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.AssemblyReference.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.GeneratedMSBuildEditorConfig.editorconfig
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfoInputs.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfo.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CoreCompileInputs.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\SerilogWebApp.StaticWebAssets.Manifest.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.input.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.output.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorCoreGenerate.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Index.cshtml.g.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Privacy.cshtml.g.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\Error.cshtml.g.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_Layout.cshtml.g.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_ValidationScriptsPartial.cshtml.g.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewImports.cshtml.g.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewStart.cshtml.g.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CopyComplete
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.genruntimeconfig.cache
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\New\Display.cshtml.g.cs
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\New\Index.cshtml.g.cs
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Logger.dll
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\Logger.pdb
D:\SerilogWebApp\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogTimings.dll
D:\SerilogWebApp\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cs
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.Development.json
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.json
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.exe
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.deps.json
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.json
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.dev.json
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Binder.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyModel.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Hosting.Abstractions.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.AspNetCore.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Environment.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Process.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Thread.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Hosting.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Logging.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Compact.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Syslog.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Settings.Configuration.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Console.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Debug.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.File.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogTimings.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Logger.dll
D:\SerilogWebApp - Copy\SerilogWebApp\bin\Debug\netcoreapp3.1\Logger.pdb
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.AssemblyReference.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.GeneratedMSBuildEditorConfig.editorconfig
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfoInputs.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfo.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CoreCompileInputs.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\SerilogWebApp.StaticWebAssets.Manifest.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.input.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.output.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorCoreGenerate.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Index.cshtml.g.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Privacy.cshtml.g.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\New\Display.cshtml.g.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\New\Index.cshtml.g.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\Error.cshtml.g.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_Layout.cshtml.g.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_ValidationScriptsPartial.cshtml.g.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewImports.cshtml.g.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewStart.cshtml.g.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cache
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cs
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CopyComplete
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\SerilogWebApp - Copy\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.genruntimeconfig.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.Development.json
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.json
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.exe
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.deps.json
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.json
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.dev.json
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Binder.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyModel.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Hosting.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.AspNetCore.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Environment.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Process.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Thread.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Hosting.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Logging.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Compact.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Syslog.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Settings.Configuration.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Console.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Debug.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.File.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogTimings.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Logger.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\bin\Debug\netcoreapp3.1\Logger.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.AssemblyReference.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.GeneratedMSBuildEditorConfig.editorconfig
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfoInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CoreCompileInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.input.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.output.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorCoreGenerate.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Index.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Privacy.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\New\Display.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\New\Index.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\Error.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_Layout.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_ValidationScriptsPartial.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewImports.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewStart.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\SerilogWebApp.StaticWebAssets.Manifest.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\SerilogWebApp.StaticWebAssets.Pack.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.SerilogWebApp.Microsoft.AspNetCore.StaticWebAssets.props
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.build.SerilogWebApp.props
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildMultiTargeting.SerilogWebApp.props
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildTransitive.SerilogWebApp.props
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CopyComplete
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.genruntimeconfig.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.Development.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\appsettings.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.exe
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.deps.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.runtimeconfig.dev.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Binder.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyModel.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Hosting.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.AspNetCore.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Environment.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Process.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Thread.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Hosting.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Extensions.Logging.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Compact.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Formatting.Syslog.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Settings.Configuration.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Console.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.Debug.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Serilog.Sinks.File.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\SerilogTimings.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Logger.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\netcoreapp3.1\Logger.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.AssemblyReference.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.GeneratedMSBuildEditorConfig.editorconfig
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfoInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.AssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CoreCompileInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorAssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.input.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.TagHelpers.output.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorCoreGenerate.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Index.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Home\Privacy.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\New\Display.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\New\Index.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\Error.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_Layout.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\Shared\_ValidationScriptsPartial.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewImports.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\Razor\Views\_ViewStart.cshtml.g.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.RazorTargetAssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.Views.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\SerilogWebApp.StaticWebAssets.Manifest.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\SerilogWebApp.StaticWebAssets.Pack.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.SerilogWebApp.Microsoft.AspNetCore.StaticWebAssets.props
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.build.SerilogWebApp.props
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildMultiTargeting.SerilogWebApp.props
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildTransitive.SerilogWebApp.props
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.csproj.CopyComplete
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\netcoreapp3.1\SerilogWebApp.genruntimeconfig.cache
