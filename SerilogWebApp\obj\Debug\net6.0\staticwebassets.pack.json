{"Files": [{"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\LICENSE", "PackagePath": "staticwebassets\\lib\\bootstrap"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "PackagePath": "staticwebassets\\lib\\jquery-validation\\LICENSE.md"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery\\LICENSE.txt"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery\\dist\\jquery.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "D:\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.map"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.SerilogWebApp.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.build.SerilogWebApp.props", "PackagePath": "build\\SerilogWebApp.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildMultiTargeting.SerilogWebApp.props", "PackagePath": "buildMultiTargeting\\SerilogWebApp.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildTransitive.SerilogWebApp.props", "PackagePath": "buildTransitive\\SerilogWebApp.props"}], "ElementsToRemove": []}