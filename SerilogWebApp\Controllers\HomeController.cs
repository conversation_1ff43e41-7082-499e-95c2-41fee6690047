﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
//using Serilog;
using SerilogWebApp.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Logger;
using Serilog;
using System.Security.Cryptography;

namespace SerilogWebApp.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }
        public class User
        {
            public int Id;
            public string Name;
            public DateTime Created;
        }
        public IActionResult Index()
        {
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
            Exception ex = new Exception();
            try
            {
                //Log.Error(ex.ToString());
                //ex = new NotSupportedException();
                //Log.Error(ex.ToString());
                //ex = new NullReferenceException();
                //Serilog.Log.Error(ex.ToString());
                //ex = new OutOfMemoryException();
                //Log.Warning(ex.ToString());
                //Logger.Logger.Log(ex.ToString());
                // Logger.SerilogIntilisation.SerilogMethod();
                Logger.Log.LogInfo(userAgent);
                Logger.Log.LogError(ex.ToString());
                Logger.Log.LogInfo("This is an information");
            }
            catch (Exception e)
            {

            }

            return View();
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
