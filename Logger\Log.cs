﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using Serilog.Formatting.Compact;
using Serilog.Formatting.Json;
using SerilogTimings;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Runtime.CompilerServices;
using System.Security.Policy;
using System.Text;
using System.Threading;

namespace Logger
{

    public static class Log
    {
        public static Microsoft.Extensions.Hosting.IHostBuilder UseGloabalLogger(this Microsoft.Extensions.Hosting.IHostBuilder app)
        {
            try
            {
                Serilog.Debugging.SelfLog.Enable(msg => Console.WriteLine(msg));

                return app.UseSerilog((hostingContext, loggerConfiguration) => loggerConfiguration
                        //.MinimumLevel.Error()
                        //.MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                        //.MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Information)

                        .ReadFrom.Configuration(hostingContext.Configuration)
                        .Enrich.WithProperty("ApplicationName", System.Reflection.Assembly.GetEntryAssembly().GetName().Name)
                        .MinimumLevel.Information()
                        .Enrich.FromLogContext()
                        .Enrich.WithProperty("Host", System.Net.Dns.GetHostName())
                        .Enrich.With(new ThreadIDEnricher())
                        .WriteTo.Console()
                        .WriteTo.DatadogLogs(
                               apiKey: hostingContext.Configuration.GetSection("Datadog").GetSection("ApiKey").Value,
                                service: hostingContext.Configuration.GetSection("Datadog").GetSection("ServiceName").Value,
                                host: System.Net.Dns.GetHostName(),
                                source: hostingContext.Configuration.GetSection("Datadog").GetSection("source").Value,
                               configuration: new Serilog.Sinks.Datadog.Logs.DatadogConfiguration
                               {
                                   Url = hostingContext.Configuration.GetSection("Datadog").GetSection("configuration").GetSection("url").Value,
                                   Port = Convert.ToInt32(hostingContext.Configuration.GetSection("Datadog").GetSection("configuration").GetSection("port").Value),
                                   UseSSL = Convert.ToBoolean(hostingContext.Configuration.GetSection("Datadog").GetSection("configuration").GetSection("useSSL").Value),
                                   UseTCP = Convert.ToBoolean(hostingContext.Configuration.GetSection("Datadog").GetSection("configuration").GetSection("useTCP").Value),

                               }
                         ));
                //.WriteTo.File(
                //        new RenderedCompactJsonFormatter(),
                //        GetLogFilePathFromJsonFile()
                //        //rollingInterval: RollingInterval.Day,
                //        //rollOnFileSizeLimit: true,
                //        //fileSizeLimitBytes: 300000,
                //       // retainedFileCountLimit: 5
                // ));
                //.WriteTo.File( GetLogFilePathFromJsonFile(),rollingInterval:RollingInterval.Day,rollOnFileSizeLimit:true,fileSizeLimitBytes: 300000,retainedFileCountLimit:5,
                //             outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} {Type} {Level:u3} {Host} {ApplicationName} PID{ThreadID} {ElapsedMilliseconds}  {Message:lj} {NewLine}{Exception}"));
            }
            catch (Exception ex)
            {
                // Log the exception to console or file for debugging
                Console.WriteLine($"Error configuring Datadog sink: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                // Consider writing to a local file as fallback
                return null;
            }
            
            }

        public static void LogError(string message, [CallerFilePath] string callerFilePath = "", [CallerMemberName] string caller = "", [CallerLineNumber] int lineNumber = 0, string logType = "Error", string username = "Application")
        {
            var fileInfo = $"{Path.GetFileName(callerFilePath)} {caller} {lineNumber}";
            message = $"{fileInfo} { message}";
            Serilog.Log.Error(message);
        }
        public static void LogInfo(string message, [CallerFilePath] string callerFilePath = "", [CallerMemberName] string caller = "", [CallerLineNumber] int lineNumber = 0, string logType = "Error", string username = "Application")
        {
            var fileInfo = $"{Path.GetFileName(callerFilePath)} {caller} {lineNumber}";
            message = $"{fileInfo} { message}";
            Serilog.Log.Information(message);
        }
        public static void LogWarning(string message, [CallerFilePath] string callerFilePath = "", [CallerMemberName] string caller = "", [CallerLineNumber] int lineNumber = 0, string logType = "Error", string username = "Application")
        {
            var fileInfo = $"{Path.GetFileName(callerFilePath)} {caller} {lineNumber}";
            message = $"{fileInfo} { message}";
            Serilog.Log.Warning(message);
        }
        public static void LogDebug(string message, [CallerFilePath] string callerFilePath = "", [CallerMemberName] string caller = "", [CallerLineNumber] int lineNumber = 0, string logType = "Error", string username = "Application")
        {
            var fileInfo = $"{Path.GetFileName(callerFilePath)} {caller} {lineNumber}";
            message = $"{fileInfo} { message}";
            Serilog.Log.Debug(message);
        }
        public static void LogFatal(string message, [CallerFilePath] string callerFilePath = "", [CallerMemberName] string caller = "", [CallerLineNumber] int lineNumber = 0, string logType = "Error", string username = "Application")
        {
            var fileInfo = $"{Path.GetFileName(callerFilePath)} {caller} {lineNumber}";
            message = $"{fileInfo} { message}";
            Serilog.Log.Fatal(message);
        }

        public static string GetLogFilePathFromJsonFile()
        {
            SerilogMvcLoggingAttribute obj = new SerilogMvcLoggingAttribute();

            var configuration = obj.GetConfigurations();
            string UseExternalPath = configuration.GetSection("LogFileInfo").GetSection("Active").Value;
            string LogFileName = configuration.GetSection("LogFileInfo").GetSection("LogFileName").Value;
            try
            {
                if (UseExternalPath.Equals("NO"))

                    return Environment.CurrentDirectory + "\\Logs\\" + LogFileName;
                else if (UseExternalPath.Equals("YES"))
                    return configuration.GetSection("LogFileInfo").GetSection("FilePath").Value +"\\"+ LogFileName;
                else
                    return Environment.CurrentDirectory + "\\Logs\\" + LogFileName;
            }
                catch (Exception ex)
                {
                    return Environment.CurrentDirectory + "\\Logs\\" + LogFileName;
            }

            return "";
        }
        public class ThreadIDEnricher : ILogEventEnricher
        {
            public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
            {
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty(
                  "ThreadID", Thread.CurrentThread.ManagedThreadId.ToString("D4")));
            }
        }
    }
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public  class SerilogMvcLoggingAttribute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var diagnosticContext = context.HttpContext.RequestServices.GetService<IDiagnosticContext>();
            diagnosticContext.Set("ActionName", context.ActionDescriptor.DisplayName);
            diagnosticContext.Set("ActionId", context.ActionDescriptor.Id);
        }
        public IConfiguration GetConfigurations()
        {
            var builder = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
            return builder.Build();
        }
    }

}
