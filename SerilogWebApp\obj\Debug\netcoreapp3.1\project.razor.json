{"SerializedFilePath": "D:\\SerilogWebApp\\SerilogWebApp\\obj\\Debug\\netcoreapp3.1\\project.razor.json", "FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\SerilogWebApp.csproj", "Configuration": {"ConfigurationName": "MVC-3.0", "LanguageVersion": "3.0", "Extensions": [{"ExtensionName": "MVC-3.0"}]}, "ProjectWorkspaceState": {"TagHelpers": [{"HashCode": 642537509, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.CascadingValue<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            A component that provides a cascading value to all descendant components.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "CascadingValue"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.CascadingValue<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            The content to which the value should be provided.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "IsFixed", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            If true, indicates that <see cref=\"P:Microsoft.AspNetCore.Components.CascadingValue`1.Value\" /> will not change. This is a\n            performance optimization that allows the framework to skip setting up\n            change notifications. Set this flag only if you will not change\n            <see cref=\"P:Microsoft.AspNetCore.Components.CascadingValue`1.Value\" /> during the component's lifetime.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "IsFixed"}}, {"Kind": "Components.Component", "Name": "Name", "TypeName": "System.String", "Documentation": "\n             <summary>\n             Optionally gives a name to the provided value. Descendant components\n             will be able to receive the value by specifying this name.\n            \n             If no name is specified, then descendant components will receive the\n             value based the type of value they are requesting.\n             </summary>\n        ", "Metadata": {"Common.PropertyName": "Name"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "TValue", "Documentation": "\n            <summary>\n            The value to be provided.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Value", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.CascadingValue<TValue>", "Components.GenericTyped": "True"}}, {"HashCode": -659802293, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.CascadingValue<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            A component that provides a cascading value to all descendant components.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.CascadingValue"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.CascadingValue<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            The content to which the value should be provided.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "IsFixed", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            If true, indicates that <see cref=\"P:Microsoft.AspNetCore.Components.CascadingValue`1.Value\" /> will not change. This is a\n            performance optimization that allows the framework to skip setting up\n            change notifications. Set this flag only if you will not change\n            <see cref=\"P:Microsoft.AspNetCore.Components.CascadingValue`1.Value\" /> during the component's lifetime.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "IsFixed"}}, {"Kind": "Components.Component", "Name": "Name", "TypeName": "System.String", "Documentation": "\n             <summary>\n             Optionally gives a name to the provided value. Descendant components\n             will be able to receive the value by specifying this name.\n            \n             If no name is specified, then descendant components will receive the\n             value based the type of value they are requesting.\n             </summary>\n        ", "Metadata": {"Common.PropertyName": "Name"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "TValue", "Documentation": "\n            <summary>\n            The value to be provided.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Value", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.CascadingValue<TValue>", "Components.GenericTyped": "True", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 909141050, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.CascadingValue<TValue>.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            The content to which the value should be provided.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "CascadingValue"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.CascadingValue<TValue>.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": -700243708, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.CascadingValue<TValue>.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            The content to which the value should be provided.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "Microsoft.AspNetCore.Components.CascadingValue"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.CascadingValue<TValue>.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 47192083, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.LayoutView", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Displays the specified content inside the specified layout and any further\n            nested layouts.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "LayoutView"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            Gets or sets the content to display.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Layout", "TypeName": "System.Type", "Documentation": "\n            <summary>\n            Gets or sets the type of the layout in which to display the content.\n            The type must implement <see cref=\"T:Microsoft.AspNetCore.Components.IComponent\" /> and accept a parameter named <see cref=\"P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Layout"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.LayoutView"}}, {"HashCode": 171641290, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.LayoutView", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Displays the specified content inside the specified layout and any further\n            nested layouts.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.LayoutView"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            Gets or sets the content to display.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Layout", "TypeName": "System.Type", "Documentation": "\n            <summary>\n            Gets or sets the type of the layout in which to display the content.\n            The type must implement <see cref=\"T:Microsoft.AspNetCore.Components.IComponent\" /> and accept a parameter named <see cref=\"P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Layout"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.LayoutView", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 1879651112, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.LayoutView.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Gets or sets the content to display.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "LayoutView"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.LayoutView.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": 1403492109, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.LayoutView.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Gets or sets the content to display.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "Microsoft.AspNetCore.Components.LayoutView"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.LayoutView.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 78791296, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.RouteView", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Displays the specified page component, rendering it inside its layout\n            and any further nested layouts.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "RouteView"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "DefaultLayout", "TypeName": "System.Type", "Documentation": "\n            <summary>\n            Gets or sets the type of a layout to be used if the page does not\n            declare any layout. If specified, the type must implement <see cref=\"T:Microsoft.AspNetCore.Components.IComponent\" />\n            and accept a parameter named <see cref=\"P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "DefaultLayout"}}, {"Kind": "Components.Component", "Name": "RouteData", "TypeName": "Microsoft.AspNetCore.Components.RouteData", "Documentation": "\n            <summary>\n            Gets or sets the route data. This determines the page that will be\n            displayed and the parameter values that will be supplied to the page.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "RouteData"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.RouteView"}}, {"HashCode": 1841896262, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.RouteView", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Displays the specified page component, rendering it inside its layout\n            and any further nested layouts.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.RouteView"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "DefaultLayout", "TypeName": "System.Type", "Documentation": "\n            <summary>\n            Gets or sets the type of a layout to be used if the page does not\n            declare any layout. If specified, the type must implement <see cref=\"T:Microsoft.AspNetCore.Components.IComponent\" />\n            and accept a parameter named <see cref=\"P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "DefaultLayout"}}, {"Kind": "Components.Component", "Name": "RouteData", "TypeName": "Microsoft.AspNetCore.Components.RouteData", "Documentation": "\n            <summary>\n            Gets or sets the route data. This determines the page that will be\n            displayed and the parameter values that will be supplied to the page.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "RouteData"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.RouteView", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": **********, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Routing.Router", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            A component that supplies route data corresponding to the current navigation state.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Router"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAssemblies", "TypeName": "System.Collections.Generic.IEnumerable<System.Reflection.Assembly>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional assemblies that should be searched for components\n            that can match URIs.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAssemblies"}}, {"Kind": "Components.Component", "Name": "AppAssembly", "TypeName": "System.Reflection.Assembly", "Documentation": "\n            <summary>\n            Gets or sets the assembly that should be searched for components matching the URI.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AppAssembly"}}, {"Kind": "Components.Component", "Name": "Found", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.RouteData>", "Documentation": "\n            <summary>\n            Gets or sets the content to display when a match is found for the requested route.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Found", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "NotFound", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            Gets or sets the content to display when no match is found for the requested route.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "NotFound", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for all child content expressions.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.Router"}}, {"HashCode": -**********, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Routing.Router", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            A component that supplies route data corresponding to the current navigation state.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Routing.Router"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAssemblies", "TypeName": "System.Collections.Generic.IEnumerable<System.Reflection.Assembly>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional assemblies that should be searched for components\n            that can match URIs.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAssemblies"}}, {"Kind": "Components.Component", "Name": "AppAssembly", "TypeName": "System.Reflection.Assembly", "Documentation": "\n            <summary>\n            Gets or sets the assembly that should be searched for components matching the URI.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AppAssembly"}}, {"Kind": "Components.Component", "Name": "Found", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.RouteData>", "Documentation": "\n            <summary>\n            Gets or sets the content to display when a match is found for the requested route.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Found", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "NotFound", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            Gets or sets the content to display when no match is found for the requested route.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "NotFound", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for all child content expressions.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.Router", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": *********, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Routing.Router.Found", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Gets or sets the content to display when a match is found for the requested route.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Found", "ParentTag": "Router"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'Found' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.Router.Found", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": -*********, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Routing.Router.Found", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Gets or sets the content to display when a match is found for the requested route.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Found", "ParentTag": "Microsoft.AspNetCore.Components.Routing.Router"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'Found' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.Router.Found", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -*********, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Routing.Router.NotFound", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Gets or sets the content to display when no match is found for the requested route.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "NotFound", "ParentTag": "Router"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.Router.NotFound", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": **********, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Routing.Router.NotFound", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "\n            <summary>\n            Gets or sets the content to display when no match is found for the requested route.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "NotFound", "ParentTag": "Microsoft.AspNetCore.Components.Routing.Router"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.Router.NotFound", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": **********, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.EditForm", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Renders a form element that cascades an <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.EditContext\" /> to descendants.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "EditForm"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created <c>form</c> element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Forms.EditContext>", "Documentation": "\n            <summary>\n            Specifies the content to be rendered inside this <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditForm\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "EditContext", "TypeName": "Microsoft.AspNetCore.Components.Forms.EditContext", "Documentation": "\n            <summary>\n            Supplies the edit context explicitly. If using this parameter, do not\n            also supply <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.Model\" />, since the model value will be taken\n            from the <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditContext.Model\" /> property.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "EditContext"}}, {"Kind": "Components.Component", "Name": "Model", "TypeName": "System.Object", "Documentation": "\n            <summary>\n            Specifies the top-level model object for the form. An edit context will\n            be constructed for this model. If using this parameter, do not also supply\n            a value for <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.EditContext\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Model"}}, {"Kind": "Components.Component", "Name": "OnInvalidSubmit", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>", "Documentation": "\n            <summary>\n            A callback that will be invoked when the form is submitted and the\n            <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.EditContext\" /> is determined to be invalid.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "OnInvalidSubmit", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "OnSubmit", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>", "Documentation": "\n             <summary>\n             A callback that will be invoked when the form is submitted.\n            \n             If using this parameter, you are responsible for triggering any validation\n             manually, e.g., by calling <see cref=\"M:Microsoft.AspNetCore.Components.Forms.EditContext.Validate\" />.\n             </summary>\n        ", "Metadata": {"Common.PropertyName": "OnSubmit", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "OnValidSubmit", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>", "Documentation": "\n            <summary>\n            A callback that will be invoked when the form is submitted and the\n            <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.EditContext\" /> is determined to be valid.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "OnValidSubmit", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for all child content expressions.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.EditForm"}}, {"HashCode": -1651790579, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.EditForm", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Renders a form element that cascades an <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.EditContext\" /> to descendants.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.EditForm"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created <c>form</c> element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Forms.EditContext>", "Documentation": "\n            <summary>\n            Specifies the content to be rendered inside this <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditForm\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "EditContext", "TypeName": "Microsoft.AspNetCore.Components.Forms.EditContext", "Documentation": "\n            <summary>\n            Supplies the edit context explicitly. If using this parameter, do not\n            also supply <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.Model\" />, since the model value will be taken\n            from the <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditContext.Model\" /> property.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "EditContext"}}, {"Kind": "Components.Component", "Name": "Model", "TypeName": "System.Object", "Documentation": "\n            <summary>\n            Specifies the top-level model object for the form. An edit context will\n            be constructed for this model. If using this parameter, do not also supply\n            a value for <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.EditContext\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Model"}}, {"Kind": "Components.Component", "Name": "OnInvalidSubmit", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>", "Documentation": "\n            <summary>\n            A callback that will be invoked when the form is submitted and the\n            <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.EditContext\" /> is determined to be invalid.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "OnInvalidSubmit", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "OnSubmit", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>", "Documentation": "\n             <summary>\n             A callback that will be invoked when the form is submitted.\n            \n             If using this parameter, you are responsible for triggering any validation\n             manually, e.g., by calling <see cref=\"M:Microsoft.AspNetCore.Components.Forms.EditContext.Validate\" />.\n             </summary>\n        ", "Metadata": {"Common.PropertyName": "OnSubmit", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "OnValidSubmit", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Forms.EditContext>", "Documentation": "\n            <summary>\n            A callback that will be invoked when the form is submitted and the\n            <see cref=\"P:Microsoft.AspNetCore.Components.Forms.EditForm.EditContext\" /> is determined to be valid.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "OnValidSubmit", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for all child content expressions.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.EditForm", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -408043690, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Forms.EditForm.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Specifies the content to be rendered inside this <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditForm\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "EditForm"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'ChildContent' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.EditForm.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": 1704917829, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Forms.EditForm.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Specifies the content to be rendered inside this <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditForm\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "Microsoft.AspNetCore.Components.Forms.EditForm"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'ChildContent' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.EditForm.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 1276695140, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputCheckbox", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            An input component for editing <see cref=\"T:System.Boolean\" /> values.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputCheckbox"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.Boolean>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<System.Boolean>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputCheckbox"}}, {"HashCode": -1075775258, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputCheckbox", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            An input component for editing <see cref=\"T:System.Boolean\" /> values.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputCheckbox"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.Boolean>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<System.Boolean>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputCheckbox", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 359071602, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputDate<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            An input component for editing date values.\n            Supported types are <see cref=\"T:System.DateTime\" /> and <see cref=\"T:System.DateTimeOffset\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputDate"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputDate<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "ParsingErrorMessage", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the error message used when displaying an a parsing error.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ParsingErrorMessage"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "TValue", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<TValue>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputDate<TValue>", "Components.GenericTyped": "True"}}, {"HashCode": 79310662, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputDate<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            An input component for editing date values.\n            Supported types are <see cref=\"T:System.DateTime\" /> and <see cref=\"T:System.DateTimeOffset\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputDate"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputDate<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "ParsingErrorMessage", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the error message used when displaying an a parsing error.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ParsingErrorMessage"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "TValue", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<TValue>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputDate<TValue>", "Components.GenericTyped": "True", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 753751873, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            An input component for editing numeric values.\n            Supported numeric types are <see cref=\"T:System.Int32\" />, <see cref=\"T:System.Int64\" />, <see cref=\"T:System.Single\" />, <see cref=\"T:System.Double\" />, <see cref=\"T:System.Decimal\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputNumber"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputNumber<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "ParsingErrorMessage", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the error message used when displaying an a parsing error.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ParsingErrorMessage"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "TValue", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<TValue>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>", "Components.GenericTyped": "True"}}, {"HashCode": 1160783662, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            An input component for editing numeric values.\n            Supported numeric types are <see cref=\"T:System.Int32\" />, <see cref=\"T:System.Int64\" />, <see cref=\"T:System.Single\" />, <see cref=\"T:System.Double\" />, <see cref=\"T:System.Decimal\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputNumber"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputNumber<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "ParsingErrorMessage", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the error message used when displaying an a parsing error.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ParsingErrorMessage"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "TValue", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<TValue>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>", "Components.GenericTyped": "True", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -1595251555, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            A dropdown selection component.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputSelect"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputSelect<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            Gets or sets the child content to be rendering inside the select element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "TValue", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<TValue>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>", "Components.GenericTyped": "True"}}, {"HashCode": 1243369879, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            A dropdown selection component.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputSelect"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.InputSelect<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            Gets or sets the child content to be rendering inside the select element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "TValue", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True", "Components.GenericTyped": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<TValue>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>", "Components.GenericTyped": "True", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -1130157769, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Gets or sets the child content to be rendering inside the select element.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "InputSelect"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": -662414230, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Gets or sets the child content to be rendering inside the select element.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "Microsoft.AspNetCore.Components.Forms.InputSelect"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -1426923454, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputText", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            An input component for editing <see cref=\"T:System.String\" /> values.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputText"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.String>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<System.String>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputText"}}, {"HashCode": 1927454378, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputText", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            An input component for editing <see cref=\"T:System.String\" /> values.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputText"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.String>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<System.String>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputText", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -1709589511, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputTextArea", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            A multiline input component for editing <see cref=\"T:System.String\" /> values.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputTextArea"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.String>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<System.String>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputTextArea"}}, {"HashCode": 2143850971, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.InputTextArea", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            A multiline input component for editing <see cref=\"T:System.String\" /> values.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputTextArea"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Value", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the value of the input. This should be used with two-way binding.\n            </summary>\n            <example>\n            @bind-Value=\"model.PropertyName\"\n            </example>\n        ", "Metadata": {"Common.PropertyName": "Value"}}, {"Kind": "Components.Component", "Name": "ValueChanged", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.String>", "Documentation": "\n            <summary>\n            Gets or sets a callback that updates the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueChanged", "Components.EventCallback": "True"}}, {"Kind": "Components.Component", "Name": "ValueExpression", "TypeName": "System.Linq.Expressions.Expression<System.Func<System.String>>", "Documentation": "\n            <summary>\n            Gets or sets an expression that identifies the bound value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ValueExpression"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputTextArea", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 49922468, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Displays a list of validation messages for a specified field within a cascaded <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditContext\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "ValidationMessage"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created <c>div</c> element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "For", "TypeName": "System.Linq.Expressions.Expression<System.Func<TValue>>", "Documentation": "\n            <summary>\n            Specifies the field for which validation messages should be displayed.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "For", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue>", "Components.GenericTyped": "True"}}, {"HashCode": 1343924420, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Displays a list of validation messages for a specified field within a cascaded <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditContext\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.ValidationMessage"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "TValue", "TypeName": "System.Type", "Documentation": "Specifies the type of the type parameter TValue for the Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue> component.", "Metadata": {"Common.PropertyName": "TValue", "Components.TypeParameter": "True", "Components.TypeParameterIsCascading": "False"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created <c>div</c> element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "For", "TypeName": "System.Linq.Expressions.Expression<System.Func<TValue>>", "Documentation": "\n            <summary>\n            Specifies the field for which validation messages should be displayed.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "For", "Components.GenericTyped": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.ValidationMessage<TValue>", "Components.GenericTyped": "True", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 1017374127, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.ValidationSummary", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Displays a list of validation messages from a cascaded <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditContext\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "ValidationSummary"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created <c>ul</c> element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Model", "TypeName": "System.Object", "Documentation": "\n            <summary>\n            Gets or sets the model to produce the list of validation messages for.\n            When specified, this lists all errors that are associated with the model instance.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Model"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.ValidationSummary"}}, {"HashCode": 522784466, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.ValidationSummary", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Displays a list of validation messages from a cascaded <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditContext\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.ValidationSummary"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be applied to the created <c>ul</c> element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "Model", "TypeName": "System.Object", "Documentation": "\n            <summary>\n            Gets or sets the model to produce the list of validation messages for.\n            When specified, this lists all errors that are associated with the model instance.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Model"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.ValidationSummary", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": **********, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Routing.NavLink", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            A component that renders an anchor tag, automatically toggling its 'active'\n            class based on whether its 'href' matches the current URI.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "NavLink"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "ActiveClass", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the CSS class name applied to the NavLink when the\n            current route matches the NavLink href.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ActiveClass"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be added to the generated\n            <c>a</c> element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            Gets or sets the child content of the component.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Match", "TypeName": "Microsoft.AspNetCore.Components.Routing.NavLinkMatch", "IsEnum": true, "Documentation": "\n            <summary>\n            Gets or sets a value representing the URL matching behavior.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Match"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.NavLink"}}, {"HashCode": **********, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Routing.NavLink", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            A component that renders an anchor tag, automatically toggling its 'active'\n            class based on whether its 'href' matches the current URI.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Routing.NavLink"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "ActiveClass", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the CSS class name applied to the NavLink when the\n            current route matches the NavLink href.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ActiveClass"}}, {"Kind": "Components.Component", "Name": "AdditionalAttributes", "TypeName": "System.Collections.Generic.IReadOnlyDictionary<System.String, System.Object>", "Documentation": "\n            <summary>\n            Gets or sets a collection of additional attributes that will be added to the generated\n            <c>a</c> element.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "AdditionalAttributes"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            Gets or sets the child content of the component.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Match", "TypeName": "Microsoft.AspNetCore.Components.Routing.NavLinkMatch", "IsEnum": true, "Documentation": "\n            <summary>\n            Gets or sets a value representing the URL matching behavior.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Match"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.NavLink", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -*********, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Routing.NavLink.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Gets or sets the child content of the component.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "NavLink"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.NavLink.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": *********, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Routing.NavLink.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "\n            <summary>\n            Gets or sets the child content of the component.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "Microsoft.AspNetCore.Components.Routing.NavLink"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Routing.NavLink.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": *********, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n             <summary>\n             Combines the behaviors of <see cref=\"T:Microsoft.AspNetCore.Components.Authorization.AuthorizeView\" /> and <see cref=\"T:Microsoft.AspNetCore.Components.RouteView\" />,\n             so that it displays the page matching the specified route but only if the user\n             is authorized to see it.\n            \n             Additionally, this component supplies a cascading parameter of type <see cref=\"T:System.Threading.Tasks.Task`1\" />,\n             which makes the user's current authentication state available to descendants.\n             </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "AuthorizeRouteView"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "Authorizing", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            The content that will be displayed while asynchronous authorization is in progress.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Authorizing", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "NotAuthorized", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is not authorized.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "NotAuthorized", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "DefaultLayout", "TypeName": "System.Type", "Documentation": "\n            <summary>\n            Gets or sets the type of a layout to be used if the page does not\n            declare any layout. If specified, the type must implement <see cref=\"T:Microsoft.AspNetCore.Components.IComponent\" />\n            and accept a parameter named <see cref=\"P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "DefaultLayout"}}, {"Kind": "Components.Component", "Name": "RouteData", "TypeName": "Microsoft.AspNetCore.Components.RouteData", "Documentation": "\n            <summary>\n            Gets or sets the route data. This determines the page that will be\n            displayed and the parameter values that will be supplied to the page.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "RouteData"}}, {"Kind": "Components.Component", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for all child content expressions.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView"}}, {"HashCode": -790334267, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n             <summary>\n             Combines the behaviors of <see cref=\"T:Microsoft.AspNetCore.Components.Authorization.AuthorizeView\" /> and <see cref=\"T:Microsoft.AspNetCore.Components.RouteView\" />,\n             so that it displays the page matching the specified route but only if the user\n             is authorized to see it.\n            \n             Additionally, this component supplies a cascading parameter of type <see cref=\"T:System.Threading.Tasks.Task`1\" />,\n             which makes the user's current authentication state available to descendants.\n             </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "Authorizing", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            The content that will be displayed while asynchronous authorization is in progress.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Authorizing", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "NotAuthorized", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is not authorized.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "NotAuthorized", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "DefaultLayout", "TypeName": "System.Type", "Documentation": "\n            <summary>\n            Gets or sets the type of a layout to be used if the page does not\n            declare any layout. If specified, the type must implement <see cref=\"T:Microsoft.AspNetCore.Components.IComponent\" />\n            and accept a parameter named <see cref=\"P:Microsoft.AspNetCore.Components.LayoutComponentBase.Body\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "DefaultLayout"}}, {"Kind": "Components.Component", "Name": "RouteData", "TypeName": "Microsoft.AspNetCore.Components.RouteData", "Documentation": "\n            <summary>\n            Gets or sets the route data. This determines the page that will be\n            displayed and the parameter values that will be supplied to the page.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "RouteData"}}, {"Kind": "Components.Component", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for all child content expressions.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 254469406, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.Authorizing", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed while asynchronous authorization is in progress.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Authorizing", "ParentTag": "AuthorizeRouteView"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.Authorizing", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": 933735593, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.Authorizing", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed while asynchronous authorization is in progress.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Authorizing", "ParentTag": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.Authorizing", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 1371580223, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.NotAuthorized", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is not authorized.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "NotAuthorized", "ParentTag": "AuthorizeRouteView"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'NotAuthorized' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.NotAuthorized", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": -942676909, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.NotAuthorized", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is not authorized.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "NotAuthorized", "ParentTag": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'NotAuthorized' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeRouteView.NotAuthorized", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 1473003150, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            Displays differing content depending on the user's authorization status.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Authorize<PERSON><PERSON><PERSON>"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "Policy", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The policy name that determines whether the content can be displayed.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Policy"}}, {"Kind": "Components.Component", "Name": "Roles", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma delimited list of roles that are allowed to display the content.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Roles"}}, {"Kind": "Components.Component", "Name": "Authorized", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is authorized.\n            If you specify a value for this parameter, do not also specify a value for <see cref=\"P:Microsoft.AspNetCore.Components.Authorization.AuthorizeViewCore.ChildContent\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Authorized", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Authorizing", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            The content that will be displayed while asynchronous authorization is in progress.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Authorizing", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is authorized.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "NotAuthorized", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is not authorized.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "NotAuthorized", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Resource", "TypeName": "System.Object", "Documentation": "\n            <summary>\n            The resource to which access is being controlled.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Resource"}}, {"Kind": "Components.Component", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for all child content expressions.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}}, {"HashCode": 635070979, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            Displays differing content depending on the user's authorization status.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "Policy", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The policy name that determines whether the content can be displayed.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Policy"}}, {"Kind": "Components.Component", "Name": "Roles", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma delimited list of roles that are allowed to display the content.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Roles"}}, {"Kind": "Components.Component", "Name": "Authorized", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is authorized.\n            If you specify a value for this parameter, do not also specify a value for <see cref=\"P:Microsoft.AspNetCore.Components.Authorization.AuthorizeViewCore.ChildContent\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Authorized", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Authorizing", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            The content that will be displayed while asynchronous authorization is in progress.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Authorizing", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is authorized.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "NotAuthorized", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment<Microsoft.AspNetCore.Components.Authorization.AuthenticationState>", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is not authorized.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "NotAuthorized", "Components.ChildContent": "True"}}, {"Kind": "Components.Component", "Name": "Resource", "TypeName": "System.Object", "Documentation": "\n            <summary>\n            The resource to which access is being controlled.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Resource"}}, {"Kind": "Components.Component", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for all child content expressions.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -1142720739, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorized", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is authorized.\n            If you specify a value for this parameter, do not also specify a value for <see cref=\"P:Microsoft.AspNetCore.Components.Authorization.AuthorizeViewCore.ChildContent\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Authorized", "ParentTag": "Authorize<PERSON><PERSON><PERSON>"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'Authorized' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorized", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": 1301732458, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorized", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is authorized.\n            If you specify a value for this parameter, do not also specify a value for <see cref=\"P:Microsoft.AspNetCore.Components.Authorization.AuthorizeViewCore.ChildContent\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Authorized", "ParentTag": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'Authorized' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorized", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -209287632, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorizing", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed while asynchronous authorization is in progress.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Authorizing", "ParentTag": "Authorize<PERSON><PERSON><PERSON>"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorizing", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": 650326898, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorizing", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed while asynchronous authorization is in progress.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Authorizing", "ParentTag": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.Authorizing", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -1411478404, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is authorized.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "Authorize<PERSON><PERSON><PERSON>"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'ChildContent' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": -624178974, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is authorized.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'ChildContent' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 2050760822, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.NotAuthorized", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is not authorized.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "NotAuthorized", "ParentTag": "Authorize<PERSON><PERSON><PERSON>"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'NotAuthorized' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.NotAuthorized", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": 1941271305, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.NotAuthorized", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content that will be displayed if the user is not authorized.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "NotAuthorized", "ParentTag": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView"}], "BoundAttributes": [{"Kind": "Components.ChildC<PERSON>nt", "Name": "Context", "TypeName": "System.String", "Documentation": "Specifies the parameter name for the 'NotAuthorized' child content expression.", "Metadata": {"Components.ChildContentParameterName": "True", "Common.PropertyName": "Context"}}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.AuthorizeView.NotAuthorized", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 514566703, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "CascadingAuthenticationState"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            The content to which the authentication state should be provided.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState"}}, {"HashCode": -273549252, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState"}], "BoundAttributes": [{"Kind": "Components.Component", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TypeName": "Microsoft.AspNetCore.Components.RenderFragment", "Documentation": "\n            <summary>\n            The content to which the authentication state should be provided.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Components.ChildContent": "True"}}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -766584723, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content to which the authentication state should be provided.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "CascadingAuthenticationState"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt"}}, {"HashCode": 292603590, "Kind": "Components.ChildC<PERSON>nt", "Name": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState.ChildContent", "AssemblyName": "Microsoft.AspNetCore.Components.Authorization", "Documentation": "\n            <summary>\n            The content to which the authentication state should be provided.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "<PERSON><PERSON><PERSON><PERSON>", "ParentTag": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState"}], "Metadata": {"Runtime.Name": "Components.None", "Common.TypeName": "Microsoft.AspNetCore.Components.Authorization.CascadingAuthenticationState.ChildContent", "Components.IsSpecialKind": "Components.ChildC<PERSON>nt", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -1570376398, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator", "AssemblyName": "Microsoft.AspNetCore.Components.Forms", "Documentation": "\n            <summary>\n            Adds Data Annotations validation support to an <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditContext\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "DataAnnotationsValidator"}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator"}}, {"HashCode": -1317134907, "Kind": "Components.Component", "Name": "Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator", "AssemblyName": "Microsoft.AspNetCore.Components.Forms", "Documentation": "\n            <summary>\n            Adds Data Annotations validation support to an <see cref=\"T:Microsoft.AspNetCore.Components.Forms.EditContext\" />.\n            </summary>\n        ", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator"}], "Metadata": {"Runtime.Name": "Components.IComponent", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 2011637847, "Kind": "Components.EventHandler", "Name": "<PERSON>ab<PERSON>", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onabort' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onabort", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onabort:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onabort:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onabort", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>", "Documentation": "Sets the '@onabort' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "<PERSON>ab<PERSON>"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onabort' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onabort' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ProgressEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 104249782, "Kind": "Components.EventHandler", "Name": "onactivate", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onactivate", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onactivate:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onactivate:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onactivate", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onactivate"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onactivate' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onactivate' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -386211422, "Kind": "Components.EventHandler", "Name": "onbeforeactivate", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onbeforeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onbeforeactivate", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforeactivate:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforeactivate:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onbeforeactivate", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onbeforeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onbeforeactivate"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforeactivate' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onbeforeactivate' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 395294726, "Kind": "Components.EventHandler", "Name": "onbeforecopy", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onbeforecopy' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onbeforecopy", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforecopy:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforecopy:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onbeforecopy", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onbeforecopy' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onbeforecopy"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforecopy' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onbeforecopy' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -196963757, "Kind": "Components.EventHandler", "Name": "onbeforecut", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onbeforecut' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onbeforecut", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforecut:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforecut:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onbeforecut", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onbeforecut' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onbeforecut"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforecut' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onbeforecut' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 832686668, "Kind": "Components.EventHandler", "Name": "onbeforedeactivate", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onbeforedeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onbeforedeactivate", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforedeactivate:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforedeactivate:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onbeforedeactivate", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onbeforedeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onbeforedeactivate"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforedeactivate' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onbeforedeactivate' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 133350078, "Kind": "Components.EventHandler", "Name": "onbeforepaste", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onbeforepaste' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onbeforepaste", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforepaste:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onbeforepaste:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onbeforepaste", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onbeforepaste' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onbeforepaste"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onbeforepaste' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onbeforepaste' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 636009819, "Kind": "Components.EventHandler", "Name": "onblur", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onblur' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onblur", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onblur:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onblur:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onblur", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.FocusEventArgs>", "Documentation": "Sets the '@onblur' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onblur"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onblur' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onblur' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.FocusEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1538588348, "Kind": "Components.EventHandler", "Name": "oncanplay", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@oncanplay' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@oncanplay", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncanplay:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncanplay:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@oncanplay", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@oncanplay' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "oncanplay"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncanplay' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@oncanplay' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 481427772, "Kind": "Components.EventHandler", "Name": "oncanplaythrough", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@oncanplaythrough' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@oncanplaythrough", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncanplaythrough:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncanplaythrough:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@oncanplaythrough", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@oncanplaythrough' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "oncanplaythrough"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncanplaythrough' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@oncanplaythrough' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -798535488, "Kind": "Components.EventHandler", "Name": "onchange", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onchange' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.ChangeEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onchange", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onchange:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onchange:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onchange", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.ChangeEventArgs>", "Documentation": "Sets the '@onchange' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.ChangeEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onchange"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onchange' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onchange' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.ChangeEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1580936022, "Kind": "Components.EventHandler", "Name": "onclick", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onclick' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onclick", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onclick:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onclick:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onclick", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>", "Documentation": "Sets the '@onclick' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onclick"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onclick' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onclick' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.MouseEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 320548796, "Kind": "Components.EventHandler", "Name": "oncontextmenu", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@oncontextmenu' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@oncontextmenu", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncontextmenu:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncontextmenu:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@oncontextmenu", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>", "Documentation": "Sets the '@oncontextmenu' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "oncontextmenu"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncontextmenu' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@oncontextmenu' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.MouseEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1632911159, "Kind": "Components.EventHandler", "Name": "oncopy", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@oncopy' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@oncopy", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncopy:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncopy:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@oncopy", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ClipboardEventArgs>", "Documentation": "Sets the '@oncopy' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "oncopy"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncopy' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@oncopy' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ClipboardEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1941393997, "Kind": "Components.EventHandler", "Name": "oncuechange", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@oncuechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@oncuechange", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncuechange:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncuechange:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@oncuechange", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@oncuechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "oncuechange"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncuechange' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@oncuechange' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1992045051, "Kind": "Components.EventHandler", "Name": "oncut", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@oncut' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@oncut", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncut:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oncut:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@oncut", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ClipboardEventArgs>", "Documentation": "Sets the '@oncut' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "oncut"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@oncut' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@oncut' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ClipboardEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -543923158, "Kind": "Components.EventHandler", "Name": "ondblclick", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondblclick' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondblclick", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondblclick:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondblclick:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondblclick", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>", "Documentation": "Sets the '@ondblclick' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondblclick"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondblclick' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondblclick' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.MouseEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -279644017, "Kind": "Components.EventHandler", "Name": "ondeactivate", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondeactivate", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondeactivate:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondeactivate:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondeactivate", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@ondeactivate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondeactivate"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondeactivate' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondeactivate' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 74517637, "Kind": "Components.EventHandler", "Name": "ondrag", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondrag' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondrag", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondrag:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondrag:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondrag", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>", "Documentation": "Sets the '@ondrag' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondrag"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondrag' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondrag' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.DragEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 879601327, "Kind": "Components.EventHandler", "Name": "ondragend", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondragend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondragend", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragend:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragend:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondragend", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>", "Documentation": "Sets the '@ondragend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondragend"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragend' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondragend' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.DragEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -318285175, "Kind": "Components.EventHandler", "Name": "ondragenter", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondragenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondragenter", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragenter:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragenter:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondragenter", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>", "Documentation": "Sets the '@ondragenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondragenter"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragenter' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondragenter' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.DragEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1419540319, "Kind": "Components.EventHandler", "Name": "ondragleave", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondragleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondragleave", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragleave:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragleave:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondragleave", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>", "Documentation": "Sets the '@ondragleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondragleave"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragleave' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondragleave' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.DragEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 409020797, "Kind": "Components.EventHandler", "Name": "ondrago<PERSON>", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondragover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondragover", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragover:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragover:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondragover", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>", "Documentation": "Sets the '@ondragover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondrago<PERSON>"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragover' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondragover' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.DragEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1324656409, "Kind": "Components.EventHandler", "Name": "ondragstart", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondragstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondragstart", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragstart:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondragstart:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondragstart", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>", "Documentation": "Sets the '@ondragstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondragstart"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondragstart' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondragstart' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.DragEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1401153931, "Kind": "Components.EventHandler", "Name": "ondrop", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondrop' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondrop", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondrop:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondrop:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondrop", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.DragEventArgs>", "Documentation": "Sets the '@ondrop' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.DragEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondrop"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondrop' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondrop' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.DragEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 145174147, "Kind": "Components.EventHandler", "Name": "ondurationchange", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ondurationchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ondurationchange", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondurationchange:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ondurationchange:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ondurationchange", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@ondurationchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ondurationchange"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ondurationchange' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ondurationchange' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1616493707, "Kind": "Components.EventHandler", "Name": "onemptied", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onemptied' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onemptied", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onemptied:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onemptied:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onemptied", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onemptied' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onemptied"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onemptied' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onemptied' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 891414409, "Kind": "Components.EventHandler", "Name": "onended", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onended' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onended", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onended:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onended:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onended", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onended' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onended"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onended' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onended' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1058047039, "Kind": "Components.EventHandler", "Name": "onerror", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onerror' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ErrorEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onerror", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onerror:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onerror:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onerror", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ErrorEventArgs>", "Documentation": "Sets the '@onerror' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ErrorEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onerror"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onerror' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onerror' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ErrorEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 469224975, "Kind": "Components.EventHandler", "Name": "onfocus", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onfocus' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onfocus", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfocus:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfocus:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onfocus", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.FocusEventArgs>", "Documentation": "Sets the '@onfocus' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onfocus"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfocus' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onfocus' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.FocusEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 546575177, "Kind": "Components.EventHandler", "Name": "onfocusin", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onfocusin' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onfocusin", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfocusin:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfocusin:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onfocusin", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.FocusEventArgs>", "Documentation": "Sets the '@onfocusin' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onfocusin"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfocusin' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onfocusin' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.FocusEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -2114769399, "Kind": "Components.EventHandler", "Name": "onfocusout", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onfocusout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onfocusout", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfocusout:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfocusout:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onfocusout", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.FocusEventArgs>", "Documentation": "Sets the '@onfocusout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.FocusEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onfocusout"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfocusout' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onfocusout' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.FocusEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 2123884659, "Kind": "Components.EventHandler", "Name": "onfullscreenchange", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onfullscreenchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onfullscreenchange", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfullscreenchange:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfullscreenchange:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onfullscreenchange", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onfullscreenchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onfullscreenchange"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfullscreenchange' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onfullscreenchange' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -463806918, "Kind": "Components.EventHandler", "Name": "onfullscreenerror", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onfullscreenerror' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onfullscreenerror", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfullscreenerror:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onfullscreenerror:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onfullscreenerror", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onfullscreenerror' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onfullscreenerror"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onfullscreenerror' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onfullscreenerror' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1871753312, "Kind": "Components.EventHandler", "Name": "ongotpointercapture", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ongotpointercapture' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ongotpointercapture", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ongotpointercapture:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ongotpointercapture:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ongotpointercapture", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@ongotpointercapture' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ongotpointercapture"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ongotpointercapture' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ongotpointercapture' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -148825719, "Kind": "Components.EventHandler", "Name": "oninput", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@oninput' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.ChangeEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@oninput", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oninput:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oninput:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@oninput", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.ChangeEventArgs>", "Documentation": "Sets the '@oninput' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.ChangeEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "oninput"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@oninput' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@oninput' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.ChangeEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1657218375, "Kind": "Components.EventHandler", "Name": "oninvalid", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@oninvalid' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@oninvalid", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oninvalid:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@oninvalid:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@oninvalid", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@oninvalid' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "oninvalid"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@oninvalid' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@oninvalid' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1104389462, "Kind": "Components.EventHandler", "Name": "onkeydown", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onkeydown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onkeydown", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onkeydown:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onkeydown:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onkeydown", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.KeyboardEventArgs>", "Documentation": "Sets the '@onkeydown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onkeydown"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onkeydown' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onkeydown' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.KeyboardEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 240557118, "Kind": "Components.EventHandler", "Name": "onkeypress", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onkeypress' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onkeypress", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onkeypress:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onkeypress:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onkeypress", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.KeyboardEventArgs>", "Documentation": "Sets the '@onkeypress' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onkeypress"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onkeypress' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onkeypress' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.KeyboardEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 120046139, "Kind": "Components.EventHandler", "Name": "onkeyup", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onkeyup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onkeyup", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onkeyup:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onkeyup:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onkeyup", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.KeyboardEventArgs>", "Documentation": "Sets the '@onkeyup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.KeyboardEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onkeyup"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onkeyup' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onkeyup' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.KeyboardEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -921956754, "Kind": "Components.EventHandler", "Name": "onload", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onload' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onload", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onload:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onload:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onload", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>", "Documentation": "Sets the '@onload' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onload"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onload' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onload' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ProgressEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -884469555, "Kind": "Components.EventHandler", "Name": "onloadeddata", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onloadeddata' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onloadeddata", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onloadeddata:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onloadeddata:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onloadeddata", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onloadeddata' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onloadeddata"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onloadeddata' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onloadeddata' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 671319647, "Kind": "Components.EventHandler", "Name": "onloadedmetadata", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onloadedmetadata' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onloadedmetadata", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onloadedmetadata:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onloadedmetadata:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onloadedmetadata", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onloadedmetadata' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onloadedmetadata"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onloadedmetadata' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onloadedmetadata' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1440136766, "Kind": "Components.EventHandler", "Name": "onloadend", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onloadend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onloadend", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onloadend:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onloadend:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onloadend", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>", "Documentation": "Sets the '@onloadend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onloadend"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onloadend' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onloadend' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ProgressEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 749652276, "Kind": "Components.EventHandler", "Name": "onloadstart", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onloadstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onloadstart", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onloadstart:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onloadstart:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onloadstart", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>", "Documentation": "Sets the '@onloadstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onloadstart"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onloadstart' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onloadstart' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ProgressEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 583309410, "Kind": "Components.EventHandler", "Name": "onlostpointercapture", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onlostpointercapture' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onlostpointercapture", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onlostpointercapture:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onlostpointercapture:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onlostpointercapture", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@onlostpointercapture' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onlostpointercapture"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onlostpointercapture' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onlostpointercapture' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1326584478, "Kind": "Components.EventHandler", "Name": "onmousedown", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onmousedown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onmousedown", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmousedown:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmousedown:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onmousedown", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>", "Documentation": "Sets the '@onmousedown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onmousedown"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmousedown' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onmousedown' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.MouseEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -2060463747, "Kind": "Components.EventHandler", "Name": "<PERSON><PERSON><PERSON><PERSON>", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onmousemove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onmousemove", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmousemove:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmousemove:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onmousemove", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>", "Documentation": "Sets the '@onmousemove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmousemove' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onmousemove' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.MouseEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 612198687, "Kind": "Components.EventHandler", "Name": "onmouseout", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onmouseout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onmouseout", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmouseout:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmouseout:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onmouseout", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>", "Documentation": "Sets the '@onmouseout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onmouseout"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmouseout' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onmouseout' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.MouseEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1550772779, "Kind": "Components.EventHandler", "Name": "on<PERSON><PERSON>ver", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onmouseover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onmouseover", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmouseover:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmouseover:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onmouseover", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>", "Documentation": "Sets the '@onmouseover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "on<PERSON><PERSON>ver"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmouseover' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onmouseover' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.MouseEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -395655512, "Kind": "Components.EventHandler", "Name": "onmouseup", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onmouseup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onmouseup", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmouseup:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmouseup:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onmouseup", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.MouseEventArgs>", "Documentation": "Sets the '@onmouseup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.MouseEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onmouseup"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmouseup' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onmouseup' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.MouseEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -834393148, "Kind": "Components.EventHandler", "Name": "onmousew<PERSON><PERSON>", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onmousewheel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.WheelEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onmousewheel", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmousewheel:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onmousewheel:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onmousewheel", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.WheelEventArgs>", "Documentation": "Sets the '@onmousewheel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.WheelEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onmousew<PERSON><PERSON>"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onmousewheel' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onmousewheel' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.WheelEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 434043723, "Kind": "Components.EventHandler", "Name": "onpaste", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpaste' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpaste", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpaste:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpaste:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpaste", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ClipboardEventArgs>", "Documentation": "Sets the '@onpaste' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ClipboardEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpaste"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpaste' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpaste' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ClipboardEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 2049924970, "Kind": "Components.EventHandler", "Name": "onpause", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpause' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpause", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpause:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpause:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpause", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onpause' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpause"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpause' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpause' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -499411268, "Kind": "Components.EventHandler", "Name": "onplay", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onplay' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onplay", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onplay:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onplay:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onplay", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onplay' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onplay"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onplay' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onplay' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -467955947, "Kind": "Components.EventHandler", "Name": "onplaying", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onplaying' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onplaying", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onplaying:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onplaying:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onplaying", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onplaying' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onplaying"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onplaying' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onplaying' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 751792818, "Kind": "Components.EventHandler", "Name": "onpointercancel", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointercancel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointercancel", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointercancel:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointercancel:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointercancel", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@onpointercancel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpointercancel"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointercancel' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointercancel' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -699986234, "Kind": "Components.EventHandler", "Name": "onpointerdown", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointerdown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointerdown", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerdown:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerdown:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointerdown", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@onpointerdown' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpointerdown"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerdown' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointerdown' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 52762667, "Kind": "Components.EventHandler", "Name": "onpointere<PERSON>", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointerenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointerenter", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerenter:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerenter:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointerenter", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@onpointerenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpointere<PERSON>"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerenter' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointerenter' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -2132332790, "Kind": "Components.EventHandler", "Name": "onpointer<PERSON>ve", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointerleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointerleave", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerleave:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerleave:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointerleave", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@onpointerleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpointer<PERSON>ve"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerleave' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointerleave' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 74752683, "Kind": "Components.EventHandler", "Name": "onpointerlockchange", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointerlockchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointerlockchange", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerlockchange:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerlockchange:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointerlockchange", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onpointerlockchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpointerlockchange"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerlockchange' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointerlockchange' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1986906860, "Kind": "Components.EventHandler", "Name": "onpoint<PERSON><PERSON><PERSON><PERSON><PERSON>", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointerlockerror' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointerlockerror", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerlockerror:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerlockerror:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointerlockerror", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onpointerlockerror' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpoint<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerlockerror' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointerlockerror' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1915518260, "Kind": "Components.EventHandler", "Name": "onpointermove", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointermove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointermove", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointermove:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointermove:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointermove", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@onpointermove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpointermove"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointermove' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointermove' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1632728403, "Kind": "Components.EventHandler", "Name": "onpointerout", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointerout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointerout", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerout:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerout:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointerout", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@onpointerout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpointerout"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerout' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointerout' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1007967319, "Kind": "Components.EventHandler", "Name": "onpointerover", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointerover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointerover", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerover:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerover:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointerover", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@onpointerover' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpointerover"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerover' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointerover' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 326400237, "Kind": "Components.EventHandler", "Name": "onpointerup", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onpointerup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onpointerup", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerup:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onpointerup:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onpointerup", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.PointerEventArgs>", "Documentation": "Sets the '@onpointerup' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.PointerEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onpointerup"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onpointerup' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onpointerup' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.PointerEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 257822301, "Kind": "Components.EventHandler", "Name": "onprogress", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onprogress' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onprogress", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onprogress:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onprogress:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onprogress", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>", "Documentation": "Sets the '@onprogress' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onprogress"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onprogress' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onprogress' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ProgressEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -646455659, "Kind": "Components.EventHandler", "Name": "onratechange", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onratechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onratechange", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onratechange:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onratechange:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onratechange", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onratechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onratechange"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onratechange' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onratechange' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 738238011, "Kind": "Components.EventHandler", "Name": "onreadystatechange", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onreadystatechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onreadystatechange", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onreadystatechange:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onreadystatechange:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onreadystatechange", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onreadystatechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onreadystatechange"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onreadystatechange' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onreadystatechange' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1575359467, "Kind": "Components.EventHandler", "Name": "onreset", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onreset' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onreset", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onreset:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onreset:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onreset", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onreset' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onreset"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onreset' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onreset' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1984892155, "Kind": "Components.EventHandler", "Name": "onscroll", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onscroll' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onscroll", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onscroll:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onscroll:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onscroll", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onscroll' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onscroll"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onscroll' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onscroll' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1318909817, "Kind": "Components.EventHandler", "Name": "onseeked", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onseeked' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onseeked", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onseeked:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onseeked:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onseeked", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onseeked' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onseeked"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onseeked' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onseeked' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -566190700, "Kind": "Components.EventHandler", "Name": "onseeking", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onseeking' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onseeking", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onseeking:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onseeking:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onseeking", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onseeking' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onseeking"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onseeking' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onseeking' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1832139381, "Kind": "Components.EventHandler", "Name": "onselect", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onselect' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onselect", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onselect:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onselect:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onselect", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onselect' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onselect"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onselect' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onselect' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 466631769, "Kind": "Components.EventHandler", "Name": "onselectionchange", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onselectionchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onselectionchange", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onselectionchange:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onselectionchange:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onselectionchange", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onselectionchange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onselectionchange"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onselectionchange' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onselectionchange' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 314395922, "Kind": "Components.EventHandler", "Name": "onselectstart", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onselectstart' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onselectstart", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onselectstart:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onselectstart:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onselectstart", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onselectstart' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onselectstart"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onselectstart' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onselectstart' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1370031643, "Kind": "Components.EventHandler", "Name": "onstalled", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onstalled' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onstalled", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onstalled:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onstalled:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onstalled", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onstalled' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onstalled"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onstalled' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onstalled' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1396958713, "Kind": "Components.EventHandler", "Name": "onstop", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onstop' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onstop", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onstop:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onstop:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onstop", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onstop' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onstop"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onstop' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onstop' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -620654161, "Kind": "Components.EventHandler", "Name": "onsubmit", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onsubmit' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onsubmit", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onsubmit:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onsubmit:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onsubmit", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onsubmit' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onsubmit"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onsubmit' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onsubmit' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -584687426, "Kind": "Components.EventHandler", "Name": "onsuspend", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onsuspend' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onsuspend", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onsuspend:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onsuspend:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onsuspend", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onsuspend' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onsuspend"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onsuspend' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onsuspend' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1279740321, "Kind": "Components.EventHandler", "Name": "ontimeout", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ontimeout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ontimeout", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontimeout:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontimeout:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ontimeout", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.ProgressEventArgs>", "Documentation": "Sets the '@ontimeout' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.ProgressEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ontimeout"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontimeout' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ontimeout' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.ProgressEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1507174455, "Kind": "Components.EventHandler", "Name": "ontimeupdate", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ontimeupdate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ontimeupdate", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontimeupdate:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontimeupdate:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ontimeupdate", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@ontimeupdate' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ontimeupdate"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontimeupdate' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ontimeupdate' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 772720459, "Kind": "Components.EventHandler", "Name": "ontouchcancel", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ontouchcancel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ontouchcancel", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchcancel:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchcancel:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ontouchcancel", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>", "Documentation": "Sets the '@ontouchcancel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ontouchcancel"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchcancel' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ontouchcancel' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.TouchEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1330823382, "Kind": "Components.EventHandler", "Name": "ontouchend", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ontouchend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ontouchend", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchend:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchend:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ontouchend", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>", "Documentation": "Sets the '@ontouchend' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ontouchend"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchend' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ontouchend' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.TouchEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1779680239, "Kind": "Components.EventHandler", "Name": "ontouche<PERSON>", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ontouchenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ontouchenter", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchenter:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchenter:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ontouchenter", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>", "Documentation": "Sets the '@ontouchenter' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ontouche<PERSON>"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchenter' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ontouchenter' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.TouchEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1488654964, "Kind": "Components.EventHandler", "Name": "ontouchleave", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ontouchleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ontouchleave", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchleave:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchleave:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ontouchleave", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>", "Documentation": "Sets the '@ontouchleave' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ontouchleave"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchleave' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ontouchleave' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.TouchEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 817317830, "Kind": "Components.EventHandler", "Name": "ontouchmove", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ontouchmove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ontouchmove", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchmove:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchmove:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ontouchmove", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>", "Documentation": "Sets the '@ontouchmove' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ontouchmove"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchmove' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ontouchmove' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.TouchEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -438970647, "Kind": "Components.EventHandler", "Name": "ontouchstart", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@ontouchstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ontouchstart", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchstart:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@ontouchstart:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@ontouchstart", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.TouchEventArgs>", "Documentation": "Sets the '@ontouchstart' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.TouchEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "ontouchstart"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@ontouchstart' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@ontouchstart' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.TouchEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": -1480061990, "Kind": "Components.EventHandler", "Name": "onvolumechange", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onvolumechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onvolumechange", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onvolumechange:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onvolumechange:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onvolumechange", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onvolumechange' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onvolumechange"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onvolumechange' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onvolumechange' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 395230637, "Kind": "Components.EventHandler", "Name": "onwaiting", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onwaiting' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onwaiting", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onwaiting:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onwaiting:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onwaiting", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.EventArgs>", "Documentation": "Sets the '@onwaiting' attribute to the provided string or delegate value. A delegate value should be of type 'System.EventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onwaiting"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onwaiting' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onwaiting' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "System.EventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 660330704, "Kind": "Components.EventHandler", "Name": "onwheel", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Sets the '@onwheel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.WheelEventArgs'.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@onwheel", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onwheel:preventDefault", "Metadata": {"Common.DirectiveAttribute": "True"}}]}, {"TagName": "*", "Attributes": [{"Name": "@onwheel:stopPropagation", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.EventHandler", "Name": "@onwheel", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<Microsoft.AspNetCore.Components.Web.WheelEventArgs>", "Documentation": "Sets the '@onwheel' attribute to the provided string or delegate value. A delegate value should be of type 'Microsoft.AspNetCore.Components.Web.WheelEventArgs'.", "Metadata": {"Components.IsWeaklyTyped": "True", "Common.DirectiveAttribute": "True", "Common.PropertyName": "onwheel"}, "BoundAttributeParameters": [{"Name": "preventDefault", "TypeName": "System.Boolean", "Documentation": "Specifies whether to cancel (if cancelable) the default action that belongs to the '@onwheel' event.", "Metadata": {"Common.PropertyName": "PreventDefault"}}, {"Name": "stopPropagation", "TypeName": "System.Boolean", "Documentation": "Specifies whether to prevent further propagation of the '@onwheel' event in the capturing and bubbling phases.", "Metadata": {"Common.PropertyName": "StopPropagation"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.EventHandler", "Components.EventHandler.EventArgs": "Microsoft.AspNetCore.Components.Web.WheelEventArgs", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.EventHandlers"}}, {"HashCode": 1270112777, "Kind": "Components.Splat", "Name": "Attributes", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Merges a collection of attributes into the current element or component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@attributes", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Splat", "Name": "@attributes", "TypeName": "System.Object", "Documentation": "Merges a collection of attributes into the current element or component.", "Metadata": {"Common.PropertyName": "Attributes", "Common.DirectiveAttribute": "True"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Splat", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Attributes"}}, {"HashCode": -1492393726, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.Razor", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting elements containing attributes with URL expected values.\n            </summary>\n            <remarks>Resolves URLs starting with '~/' (relative to the application's 'webroot' setting) that are not\n            targeted by other <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" />s. Runs prior to other <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" />s to ensure\n            application-relative URLs are resolved.</remarks>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "itemid", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "a", "Attributes": [{"Name": "href", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "applet", "Attributes": [{"Name": "archive", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "area", "TagStructure": 2, "Attributes": [{"Name": "href", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "audio", "Attributes": [{"Name": "src", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "base", "TagStructure": 2, "Attributes": [{"Name": "href", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "blockquote", "Attributes": [{"Name": "cite", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "button", "Attributes": [{"Name": "formaction", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "del", "Attributes": [{"Name": "cite", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "embed", "TagStructure": 2, "Attributes": [{"Name": "src", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "form", "Attributes": [{"Name": "action", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "html", "Attributes": [{"Name": "manifest", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "iframe", "Attributes": [{"Name": "src", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "img", "TagStructure": 2, "Attributes": [{"Name": "src", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "img", "TagStructure": 2, "Attributes": [{"Name": "srcset", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "formaction", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "src", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "ins", "Attributes": [{"Name": "cite", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "href", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "menuitem", "Attributes": [{"Name": "icon", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "object", "Attributes": [{"Name": "archive", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "object", "Attributes": [{"Name": "data", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "q", "Attributes": [{"Name": "cite", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "script", "Attributes": [{"Name": "src", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "source", "TagStructure": 2, "Attributes": [{"Name": "src", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "source", "TagStructure": 2, "Attributes": [{"Name": "srcset", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "track", "TagStructure": 2, "Attributes": [{"Name": "src", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "video", "Attributes": [{"Name": "poster", "Value": "~/", "ValueComparison": 2}]}, {"TagName": "video", "Attributes": [{"Name": "src", "Value": "~/", "ValueComparison": 2}]}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper"}}, {"HashCode": -35914922, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;a&gt; elements.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "a", "Attributes": [{"Name": "asp-action"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-all-route-data"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-area"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-controller"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-fragment"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-host"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-page"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-page-handler"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-protocol"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-route"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-route-", "NameComparison": 1}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-action", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the action method.\n            </summary>\n            <remarks>\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Page\" /> is non-<c>null</c>.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Action"}}, {"Kind": "ITagHelper", "Name": "asp-area", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the area.\n            </summary>\n            <remarks>\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> is non-<c>null</c>.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Area"}}, {"Kind": "ITagHelper", "Name": "asp-controller", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the controller.\n            </summary>\n            <remarks>\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Page\" /> is non-<c>null</c>.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Controller"}}, {"Kind": "ITagHelper", "Name": "asp-fragment", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The URL fragment name.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Fragment"}}, {"Kind": "ITagHelper", "Name": "asp-host", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The host name.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Host"}}, {"Kind": "ITagHelper", "Name": "asp-page", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the page.\n            </summary>\n            <remarks>\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Action\" />, <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Controller\" />\n            is non-<c>null</c>.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Page"}}, {"Kind": "ITagHelper", "Name": "asp-page-handler", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the page handler.\n            </summary>\n            <remarks>\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Action\" />, or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Controller\" />\n            is non-<c>null</c>.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>"}}, {"Kind": "ITagHelper", "Name": "asp-protocol", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The protocol for the URL, such as \"http\" or \"https\".\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Protocol"}}, {"Kind": "ITagHelper", "Name": "asp-route", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Name of the route.\n            </summary>\n            <remarks>\n            Must be <c>null</c> if one of <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Action\" />, <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Controller\" />, <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Area\" /> \n            or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Page\" /> is non-<c>null</c>.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Route"}}, {"Kind": "ITagHelper", "Name": "asp-all-route-data", "TypeName": "System.Collections.Generic.IDictionary<System.String, System.String>", "IndexerNamePrefix": "asp-route-", "IndexerTypeName": "System.String", "Documentation": "\n            <summary>\n            Additional parameters for the route.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "RouteValues"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper"}}, {"HashCode": -886810268, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.TagHelper\" /> implementation targeting &lt;cache&gt; elements.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "cache"}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "priority", "TypeName": "Microsoft.Extensions.Caching.Memory.CacheItemPriority?", "Documentation": "\n            <summary>\n            Gets or sets the <see cref=\"T:Microsoft.Extensions.Caching.Memory.CacheItemPriority\" /> policy for the cache entry.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Priority"}}, {"Kind": "ITagHelper", "Name": "enabled", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Gets or sets the value which determines if the tag helper is enabled or not.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Enabled"}}, {"Kind": "ITagHelper", "Name": "expires-after", "TypeName": "System.TimeSpan?", "Documentation": "\n            <summary>\n            Gets or sets the duration, from the time the cache entry was added, when it should be evicted.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ExpiresAfter"}}, {"Kind": "ITagHelper", "Name": "expires-on", "TypeName": "System.DateTimeOffset?", "Documentation": "\n            <summary>\n            Gets or sets the exact <see cref=\"T:System.DateTimeOffset\" /> the cache entry should be evicted.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ExpiresOn"}}, {"Kind": "ITagHelper", "Name": "expires-sliding", "TypeName": "System.TimeSpan?", "Documentation": "\n            <summary>\n            Gets or sets the duration from last access that the cache entry should be evicted.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ExpiresSliding"}}, {"Kind": "ITagHelper", "Name": "vary-by", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a <see cref=\"T:System.String\" /> to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryBy"}}, {"Kind": "ITagHelper", "Name": "vary-by-cookie", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a comma-delimited set of cookie names to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByCookie"}}, {"Kind": "ITagHelper", "Name": "vary-by-culture", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Gets or sets a value that determines if the cached result is to be varied by request culture.\n            <para>\n            Setting this to <c>true</c> would result in the result to be varied by <see cref=\"P:System.Globalization.CultureInfo.CurrentCulture\" />\n            and <see cref=\"P:System.Globalization.CultureInfo.CurrentUICulture\" />.\n            </para>\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByCulture"}}, {"Kind": "ITagHelper", "Name": "vary-by-header", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a comma-delimited set of HTTP request headers to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByHeader"}}, {"Kind": "ITagHelper", "Name": "vary-by-query", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a comma-delimited set of query parameters to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryBy<PERSON>uery"}}, {"Kind": "ITagHelper", "Name": "vary-by-route", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a comma-delimited set of route data parameters to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByRoute"}}, {"Kind": "ITagHelper", "Name": "vary-by-user", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Gets or sets a value that determines if the cached result is to be varied by the Identity for the logged in\n            <see cref=\"P:Microsoft.AspNetCore.Http.HttpContext.User\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByUser"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper"}}, {"HashCode": -1978267100, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.ComponentTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            A <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.TagHelper\" /> that renders a Razor component.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "component", "TagStructure": 2, "Attributes": [{"Name": "type"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "type", "TypeName": "System.Type", "Documentation": "\n            <summary>\n            Gets or sets the component type. This value is required.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ComponentType"}}, {"Kind": "ITagHelper", "Name": "params", "TypeName": "System.Collections.Generic.IDictionary<System.String, System.Object>", "IndexerNamePrefix": "param-", "IndexerTypeName": "System.Object", "Documentation": "\n            <summary>\n            Gets or sets values for component parameters.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Parameters"}}, {"Kind": "ITagHelper", "Name": "render-mode", "TypeName": "Microsoft.AspNetCore.Mvc.Rendering.RenderMode", "IsEnum": true, "Documentation": "\n            <summary>\n            Gets or sets the <see cref=\"T:Microsoft.AspNetCore.Mvc.Rendering.RenderMode\" />\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "RenderMode"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.ComponentTagHelper"}}, {"HashCode": 828801069, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.TagHelper\" /> implementation targeting &lt;distributed-cache&gt; elements.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "distributed-cache", "Attributes": [{"Name": "name"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a unique name to discriminate cached entries.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Name"}}, {"Kind": "ITagHelper", "Name": "enabled", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Gets or sets the value which determines if the tag helper is enabled or not.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Enabled"}}, {"Kind": "ITagHelper", "Name": "expires-after", "TypeName": "System.TimeSpan?", "Documentation": "\n            <summary>\n            Gets or sets the duration, from the time the cache entry was added, when it should be evicted.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ExpiresAfter"}}, {"Kind": "ITagHelper", "Name": "expires-on", "TypeName": "System.DateTimeOffset?", "Documentation": "\n            <summary>\n            Gets or sets the exact <see cref=\"T:System.DateTimeOffset\" /> the cache entry should be evicted.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ExpiresOn"}}, {"Kind": "ITagHelper", "Name": "expires-sliding", "TypeName": "System.TimeSpan?", "Documentation": "\n            <summary>\n            Gets or sets the duration from last access that the cache entry should be evicted.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ExpiresSliding"}}, {"Kind": "ITagHelper", "Name": "vary-by", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a <see cref=\"T:System.String\" /> to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryBy"}}, {"Kind": "ITagHelper", "Name": "vary-by-cookie", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a comma-delimited set of cookie names to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByCookie"}}, {"Kind": "ITagHelper", "Name": "vary-by-culture", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Gets or sets a value that determines if the cached result is to be varied by request culture.\n            <para>\n            Setting this to <c>true</c> would result in the result to be varied by <see cref=\"P:System.Globalization.CultureInfo.CurrentCulture\" />\n            and <see cref=\"P:System.Globalization.CultureInfo.CurrentUICulture\" />.\n            </para>\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByCulture"}}, {"Kind": "ITagHelper", "Name": "vary-by-header", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a comma-delimited set of HTTP request headers to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByHeader"}}, {"Kind": "ITagHelper", "Name": "vary-by-query", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a comma-delimited set of query parameters to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryBy<PERSON>uery"}}, {"Kind": "ITagHelper", "Name": "vary-by-route", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets a comma-delimited set of route data parameters to vary the cached result by.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByRoute"}}, {"Kind": "ITagHelper", "Name": "vary-by-user", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Gets or sets a value that determines if the cached result is to be varied by the Identity for the logged in\n            <see cref=\"P:Microsoft.AspNetCore.Http.HttpContext.User\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "VaryByUser"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper"}}, {"HashCode": 1883741348, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;environment&gt; elements that conditionally renders\n            content based on the current value of <see cref=\"P:Microsoft.AspNetCore.Hosting.IHostingEnvironment.EnvironmentName\" />.\n            If the environment is not listed in the specified <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Names\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Include\" />, \n            or if it is in <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Exclude\" />, the content will not be rendered.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "environment"}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "exclude", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of environment names in which the content will not be rendered.\n            </summary>\n            <remarks>\n            The specified environment names are compared case insensitively to the current value of\n            <see cref=\"P:Microsoft.AspNetCore.Hosting.IHostingEnvironment.EnvironmentName\" />.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Exclude"}}, {"Kind": "ITagHelper", "Name": "include", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of environment names in which the content should be rendered.\n            If the current environment is also in the <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Exclude\" /> list, the content will not be rendered.\n            </summary>\n            <remarks>\n            The specified environment names are compared case insensitively to the current value of\n            <see cref=\"P:Microsoft.AspNetCore.Hosting.IHostingEnvironment.EnvironmentName\" />.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Include"}}, {"Kind": "ITagHelper", "Name": "names", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of environment names in which the content should be rendered.\n            If the current environment is also in the <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Exclude\" /> list, the content will not be rendered.\n            </summary>\n            <remarks>\n            The specified environment names are compared case insensitively to the current value of\n            <see cref=\"P:Microsoft.AspNetCore.Hosting.IHostingEnvironment.EnvironmentName\" />.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Names"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper"}}, {"HashCode": -1019503519, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;button&gt; elements and &lt;input&gt; elements with\n            their <c>type</c> attribute set to <c>image</c> or <c>submit</c>.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "button", "Attributes": [{"Name": "asp-action"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-all-route-data"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-area"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-controller"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-fragment"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-page"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-page-handler"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-route"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-route-", "NameComparison": 1}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-action"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-all-route-data"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-area"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-controller"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-fragment"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-page"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-page-handler"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-route"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-route-", "NameComparison": 1}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-action"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-all-route-data"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-area"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-controller"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-fragment"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-page"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-page-handler"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-route"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-route-", "NameComparison": 1}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-action", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the action method.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Action"}}, {"Kind": "ITagHelper", "Name": "asp-area", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the area.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Area"}}, {"Kind": "ITagHelper", "Name": "asp-controller", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the controller.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Controller"}}, {"Kind": "ITagHelper", "Name": "asp-fragment", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the URL fragment.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Fragment"}}, {"Kind": "ITagHelper", "Name": "asp-page", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the page.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Page"}}, {"Kind": "ITagHelper", "Name": "asp-page-handler", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the page handler.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>"}}, {"Kind": "ITagHelper", "Name": "asp-route", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Name of the route.\n            </summary>\n            <remarks>\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Action\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Controller\" /> is non-<c>null</c>.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Route"}}, {"Kind": "ITagHelper", "Name": "asp-all-route-data", "TypeName": "System.Collections.Generic.IDictionary<System.String, System.String>", "IndexerNamePrefix": "asp-route-", "IndexerTypeName": "System.String", "Documentation": "\n            <summary>\n            Additional parameters for the route.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "RouteValues"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper"}}, {"HashCode": -893863903, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;form&gt; elements.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "form"}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-action", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the action method.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Action"}}, {"Kind": "ITagHelper", "Name": "asp-antiforgery", "TypeName": "System.Boolean?", "Documentation": "\n            <summary>\n            Whether the antiforgery token should be generated.\n            </summary>\n            <value>Defaults to <c>false</c> if user provides an <c>action</c> attribute\n            or if the <c>method</c> is <see cref=\"F:Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Get\" />; <c>true</c> otherwise.</value>\n        ", "Metadata": {"Common.PropertyName": "Antiforgery"}}, {"Kind": "ITagHelper", "Name": "asp-area", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the area.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Area"}}, {"Kind": "ITagHelper", "Name": "asp-controller", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the controller.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Controller"}}, {"Kind": "ITagHelper", "Name": "asp-fragment", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Gets or sets the URL fragment.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Fragment"}}, {"Kind": "ITagHelper", "Name": "asp-page", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the page.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Page"}}, {"Kind": "ITagHelper", "Name": "asp-page-handler", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the page handler.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>"}}, {"Kind": "ITagHelper", "Name": "asp-route", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Name of the route.\n            </summary>\n            <remarks>\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Action\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Controller\" /> is non-<c>null</c>.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Route"}}, {"Kind": "ITagHelper", "Name": "asp-all-route-data", "TypeName": "System.Collections.Generic.IDictionary<System.String, System.String>", "IndexerNamePrefix": "asp-route-", "IndexerTypeName": "System.String", "Documentation": "\n            <summary>\n            Additional parameters for the route.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "RouteValues"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper"}}, {"HashCode": 1432464087, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;img&gt; elements that supports file versioning.\n            </summary>\n            <remarks>\n            The tag helper won't process for cases with just the 'src' attribute.\n            </remarks>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "img", "TagStructure": 2, "Attributes": [{"Name": "asp-append-version"}, {"Name": "src"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-append-version", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Value indicating if file version should be appended to the src urls.\n            </summary>\n            <remarks>\n            If <c>true</c> then a query string \"v\" with the encoded content of the file is added.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "AppendVersion"}}, {"Kind": "ITagHelper", "Name": "src", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Source of the image.\n            </summary>\n            <remarks>\n            Passed through to the generated HTML in all cases.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Src"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper"}}, {"HashCode": 1006186794, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;input&gt; elements with an <c>asp-for</c> attribute.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "asp-for"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "\n            <summary>\n            An expression to be evaluated against the current model.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "For"}}, {"Kind": "ITagHelper", "Name": "asp-format", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The format string (see https://msdn.microsoft.com/en-us/library/txafckwd.aspx) used to format the\n            <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.For\" /> result. Sets the generated \"value\" attribute to that formatted string.\n            </summary>\n            <remarks>\n            Not used if the provided (see <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.InputTypeName\" />) or calculated \"type\" attribute value is\n            <c>checkbox</c>, <c>password</c>, or <c>radio</c>. That is, <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.Format\" /> is used when calling\n            <see cref=\"M:Microsoft.AspNetCore.Mvc.ViewFeatures.IHtmlGenerator.GenerateTextBox(Microsoft.AspNetCore.Mvc.Rendering.ViewContext,Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExplorer,System.String,System.Object,System.String,System.Object)\" />.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Format"}}, {"Kind": "ITagHelper", "Name": "type", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The type of the &lt;input&gt; element.\n            </summary>\n            <remarks>\n            Passed through to the generated HTML in all cases. Also used to determine the <see cref=\"T:Microsoft.AspNetCore.Mvc.ViewFeatures.IHtmlGenerator\" />\n            helper to call and the default <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.Format\" /> value. A default <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.Format\" /> is not calculated\n            if the provided (see <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.InputTypeName\" />) or calculated \"type\" attribute value is <c>checkbox</c>,\n            <c>hidden</c>, <c>password</c>, or <c>radio</c>.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "InputTypeName"}}, {"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the &lt;input&gt; element.\n            </summary>\n            <remarks>\n            Passed through to the generated HTML in all cases. Also used to determine whether <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.For\" /> is\n            valid with an empty <see cref=\"P:Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression.Name\" />.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Name"}}, {"Kind": "ITagHelper", "Name": "value", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The value of the &lt;input&gt; element.\n            </summary>\n            <remarks>\n            Passed through to the generated HTML in all cases. Also used to determine the generated \"checked\" attribute\n            if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.InputTypeName\" /> is \"radio\". Must not be <c>null</c> in that case.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper"}}, {"HashCode": -334796707, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;label&gt; elements with an <c>asp-for</c> attribute.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "label", "Attributes": [{"Name": "asp-for"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "\n            <summary>\n            An expression to be evaluated against the current model.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "For"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper"}}, {"HashCode": -1540697902, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;link&gt; elements that supports fallback href paths.\n            </summary>\n            <remarks>\n            The tag helper won't process for cases with just the 'href' attribute.\n            </remarks>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-append-version"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-href"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-href-exclude"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-href-include"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-test-class"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-test-property"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-test-value"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-href-exclude"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-href-include"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-append-version", "TypeName": "System.Boolean?", "Documentation": "\n            <summary>\n            Value indicating if file version should be appended to the href urls.\n            </summary>\n            <remarks>\n            If <c>true</c> then a query string \"v\" with the encoded content of the file is added.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "AppendVersion"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-href", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The URL of a CSS stylesheet to fallback to in the case the primary one fails.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackHref"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-href-exclude", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of globbed file patterns of CSS stylesheets to exclude from the fallback list, in\n            the case the primary one fails.\n            The glob patterns are assessed relative to the application's 'webroot' setting.\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefInclude\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackHrefExclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-href-include", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of globbed file patterns of CSS stylesheets to fallback to in the case the primary\n            one fails.\n            The glob patterns are assessed relative to the application's 'webroot' setting.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackHrefInclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-test-class", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The class name defined in the stylesheet to use for the fallback test.\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestProperty\" /> and <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestValue\" />,\n            and either <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHref\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefInclude\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackTestClass"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-test-property", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The CSS property name to use for the fallback test.\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestClass\" /> and <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestValue\" />,\n            and either <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHref\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefInclude\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackTestProperty"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-test-value", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The CSS property value to use for the fallback test.\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestClass\" /> and <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestProperty\" />,\n            and either <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHref\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefInclude\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackTestValue"}}, {"Kind": "ITagHelper", "Name": "href", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Address of the linked resource.\n            </summary>\n            <remarks>\n            Passed through to the generated HTML in all cases.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON>"}}, {"Kind": "ITagHelper", "Name": "asp-href-exclude", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of globbed file patterns of CSS stylesheets to exclude from loading.\n            The glob patterns are assessed relative to the application's 'webroot' setting.\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.HrefInclude\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "HrefExclude"}}, {"Kind": "ITagHelper", "Name": "asp-href-include", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of globbed file patterns of CSS stylesheets to load.\n            The glob patterns are assessed relative to the application's 'webroot' setting.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "HrefInclude"}}, {"Kind": "ITagHelper", "Name": "asp-suppress-fallback-integrity", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Boolean value that determines if an integrity hash will be compared with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHref\" /> value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "SuppressFallbackIntegrity"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper"}}, {"HashCode": 394668654, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;option&gt; elements.\n            </summary>\n            <remarks>\n            This <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> works in conjunction with <see cref=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper\" />. It reads elements\n            content but does not modify that content. The only modification it makes is to add a <c>selected</c> attribute\n            in some cases.\n            </remarks>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "option"}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "value", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Specifies a value for the &lt;option&gt; element.\n            </summary>\n            <remarks>\n            Passed through to the generated HTML in all cases.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper"}}, {"HashCode": -188784937, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            Renders a partial view.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "partial", "TagStructure": 2, "Attributes": [{"Name": "name"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "fallback-name", "TypeName": "System.String", "Documentation": "\n            <summary>\n            View to lookup if the view specified by <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.Name\" /> cannot be located.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackName"}}, {"Kind": "ITagHelper", "Name": "for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "\n            <summary>\n            An expression to be evaluated against the current model. Cannot be used together with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.Model\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "For"}}, {"Kind": "ITagHelper", "Name": "model", "TypeName": "System.Object", "Documentation": "\n            <summary>\n            The model to pass into the partial view. Cannot be used together with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.For\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Model"}}, {"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name or path of the partial view that is rendered to the response.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Name"}}, {"Kind": "ITagHelper", "Name": "optional", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            When optional, executing the tag helper will no-op if the view cannot be located. \n            Otherwise will throw stating the view could not be found.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Optional"}}, {"Kind": "ITagHelper", "Name": "view-data", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary", "IndexerNamePrefix": "view-data-", "IndexerTypeName": "System.Object", "Documentation": "\n            <summary>\n            A <see cref=\"T:Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary\" /> to pass into the partial view.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "ViewData"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper"}}, {"HashCode": -466907409, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;script&gt; elements that supports fallback src paths.\n            </summary>\n            <remarks>\n            The tag helper won't process for cases with just the 'src' attribute.\n            </remarks>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "script", "Attributes": [{"Name": "asp-append-version"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-fallback-src"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-fallback-src-exclude"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-fallback-src-include"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-fallback-test"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-src-exclude"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-src-include"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-append-version", "TypeName": "System.Boolean?", "Documentation": "\n            <summary>\n            Value indicating if file version should be appended to src urls.\n            </summary>\n            <remarks>\n            A query string \"v\" with the encoded content of the file is added.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "AppendVersion"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-src", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The URL of a Script tag to fallback to in the case the primary one fails.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackSrc"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-src-exclude", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of globbed file patterns of JavaScript scripts to exclude from the fallback list, in\n            the case the primary one fails.\n            The glob patterns are assessed relative to the application's 'webroot' setting.\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.FallbackSrcInclude\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackSrcExclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-src-include", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of globbed file patterns of JavaScript scripts to fallback to in the case the\n            primary one fails.\n            The glob patterns are assessed relative to the application's 'webroot' setting.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackSrcInclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-test", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The script method defined in the primary script to use for the fallback test.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "FallbackTestExpression"}}, {"Kind": "ITagHelper", "Name": "src", "TypeName": "System.String", "Documentation": "\n            <summary>\n            Address of the external script to use.\n            </summary>\n            <remarks>\n            Passed through to the generated HTML in all cases.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Src"}}, {"Kind": "ITagHelper", "Name": "asp-src-exclude", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of globbed file patterns of JavaScript scripts to exclude from loading.\n            The glob patterns are assessed relative to the application's 'webroot' setting.\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.SrcInclude\" />.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "SrcExclude"}}, {"Kind": "ITagHelper", "Name": "asp-src-include", "TypeName": "System.String", "Documentation": "\n            <summary>\n            A comma separated list of globbed file patterns of JavaScript scripts to load.\n            The glob patterns are assessed relative to the application's 'webroot' setting.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "SrcInclude"}}, {"Kind": "ITagHelper", "Name": "asp-suppress-fallback-integrity", "TypeName": "System.Boolean", "Documentation": "\n            <summary>\n            Boolean value that determines if an integrity hash will be compared with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.FallbackSrc\" /> value.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "SuppressFallbackIntegrity"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper"}}, {"HashCode": 1854126375, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;select&gt; elements with <c>asp-for</c> and/or\n            <c>asp-items</c> attribute(s).\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "select", "Attributes": [{"Name": "asp-for"}]}, {"TagName": "select", "Attributes": [{"Name": "asp-items"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "\n            <summary>\n            An expression to be evaluated against the current model.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "For"}}, {"Kind": "ITagHelper", "Name": "asp-items", "TypeName": "System.Collections.Generic.IEnumerable<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>", "Documentation": "\n            <summary>\n            A collection of <see cref=\"T:Microsoft.AspNetCore.Mvc.Rendering.SelectListItem\" /> objects used to populate the &lt;select&gt; element with\n            &lt;optgroup&gt; and &lt;option&gt; elements.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "Items"}}, {"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the &lt;input&gt; element.\n            </summary>\n            <remarks>\n            Passed through to the generated HTML in all cases. Also used to determine whether <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper.For\" /> is\n            valid with an empty <see cref=\"P:Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression.Name\" />.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Name"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper"}}, {"HashCode": 628075759, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;textarea&gt; elements with an <c>asp-for</c> attribute.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "textarea", "Attributes": [{"Name": "asp-for"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "\n            <summary>\n            An expression to be evaluated against the current model.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "For"}}, {"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "\n            <summary>\n            The name of the &lt;input&gt; element.\n            </summary>\n            <remarks>\n            Passed through to the generated HTML in all cases. Also used to determine whether <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper.For\" /> is\n            valid with an empty <see cref=\"P:Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression.Name\" />.\n            </remarks>\n        ", "Metadata": {"Common.PropertyName": "Name"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper"}}, {"HashCode": 1381111830, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.ValidationMessageTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting any HTML element with an <c>asp-validation-for</c>\n            attribute.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "span", "Attributes": [{"Name": "asp-validation-for"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-validation-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "\n            <summary>\n            Gets an expression to be evaluated against the current model.\n            </summary>\n        ", "Metadata": {"Common.PropertyName": "For"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.ValidationMessageTagHelper"}}, {"HashCode": 536842639, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "\n            <summary>\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting any HTML element with an <c>asp-validation-summary</c>\n            attribute.\n            </summary>\n        ", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "div", "Attributes": [{"Name": "asp-validation-summary"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-validation-summary", "TypeName": "Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary", "IsEnum": true, "Documentation": "\n            <summary>\n            If <see cref=\"F:Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary.All\" /> or <see cref=\"F:Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary.ModelOnly\" />, appends a validation\n            summary. Otherwise (<see cref=\"F:Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary.None\" />, the default), this tag helper does nothing.\n            </summary>\n            <exception cref=\"T:System.ArgumentException\">\n            Thrown if setter is called with an undefined <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper.ValidationSummary\" /> value e.g.\n            <c>(ValidationSummary)23</c>.\n            </exception>\n        ", "Metadata": {"Common.PropertyName": "ValidationSummary"}}], "Metadata": {"Runtime.Name": "ITagHelper", "Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper"}}, {"HashCode": 999350350, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to an attribute and a change event, based on the naming of the bind attribute. For example: <code>@bind-value=\"...\"</code> and <code>@bind-value:event=\"onchange\"</code> will assign the current value of the expression to the 'value' attribute, and assign a delegate that attempts to set the value to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@bind-", "NameComparison": 1, "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-...", "TypeName": "System.Collections.Generic.Dictionary<string, object>", "IndexerNamePrefix": "@bind-", "IndexerTypeName": "System.Object", "Documentation": "Binds the provided expression to an attribute and a change event, based on the naming of the bind attribute. For example: <code>@bind-value=\"...\"</code> and <code>@bind-value:event=\"onchange\"</code> will assign the current value of the expression to the 'value' attribute, and assign a delegate that attempts to set the value to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the corresponding bind attribute. For example: <code>@bind-value:format=\"...\"</code> will apply a format string to the value specified in <code>@bind-value=\"...\"</code>. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind-...' attribute.", "Metadata": {"Common.PropertyName": "Event"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.Fallback": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Bind"}}, {"HashCode": -1488907878, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "select", "Attributes": [{"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "False", "Components.Bind.Format": null, "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": -596367609, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "textarea", "Attributes": [{"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "False", "Components.Bind.Format": null, "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": 1525564843, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'checked' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "checkbox", "ValueComparison": 1}, {"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'checked' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_checked"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_checked"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-checked", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_checked"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "checked", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "False", "Components.Bind.Format": null, "Components.Bind.TypeAttribute": "checkbox", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": -570582221, "Kind": "Components.Bind", "Name": "Bind_value", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "date", "ValueComparison": 1}, {"Name": "@bind-value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-value", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind_value"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": "yyyy-MM-dd", "Components.Bind.TypeAttribute": "date", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": -259225247, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "date", "ValueComparison": 1}, {"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": "yyyy-MM-dd", "Components.Bind.TypeAttribute": "date", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": 1169474289, "Kind": "Components.Bind", "Name": "Bind_value", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "datetime-local", "ValueComparison": 1}, {"Name": "@bind-value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-value", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind_value"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": "yyyy-MM-ddTHH:mm:ss", "Components.Bind.TypeAttribute": "datetime-local", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": 564364738, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "datetime-local", "ValueComparison": 1}, {"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": "yyyy-MM-ddTHH:mm:ss", "Components.Bind.TypeAttribute": "datetime-local", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": -1515522350, "Kind": "Components.Bind", "Name": "Bind_value", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "month", "ValueComparison": 1}, {"Name": "@bind-value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-value", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind_value"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": "yyyy-MM", "Components.Bind.TypeAttribute": "month", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": 871231720, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "month", "ValueComparison": 1}, {"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": "yyyy-MM", "Components.Bind.TypeAttribute": "month", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": 2003827684, "Kind": "Components.Bind", "Name": "Bind_value", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "number", "ValueComparison": 1}, {"Name": "@bind-value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-value", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind_value"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": null, "Components.Bind.TypeAttribute": "number", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": 1758301023, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "number", "ValueComparison": 1}, {"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": null, "Components.Bind.TypeAttribute": "number", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": -2050403530, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "text", "ValueComparison": 1}, {"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "False", "Components.Bind.Format": null, "Components.Bind.TypeAttribute": "text", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": 1296198824, "Kind": "Components.Bind", "Name": "Bind_value", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "time", "ValueComparison": 1}, {"Name": "@bind-value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-value", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind_value"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": "HH:mm:ss", "Components.Bind.TypeAttribute": "time", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": -1647736512, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "type", "Value": "time", "ValueComparison": 1}, {"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "True", "Components.Bind.Format": "HH:mm:ss", "Components.Bind.TypeAttribute": "time", "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": 688756199, "Kind": "Components.Bind", "Name": "Bind_value", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "@bind-value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-value", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind_value"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind-value' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind-value' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "False", "Components.Bind.Format": null, "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": -1874581601, "Kind": "Components.Bind", "Name": "Bind", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "input", "Attributes": [{"Name": "@bind", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind", "TypeName": "System.Object", "Documentation": "Binds the provided expression to the 'value' attribute and a change event delegate to the 'onchange' attribute.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Bind"}, "BoundAttributeParameters": [{"Name": "format", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}, {"Name": "event", "TypeName": "System.String", "Documentation": "Specifies the event handler name to attach for change notifications for the value provided by the '@bind' attribute.", "Metadata": {"Common.PropertyName": "Event_value"}}, {"Name": "culture", "TypeName": "System.Globalization.CultureInfo", "Documentation": "Specifies the culture to use for conversions.", "Metadata": {"Common.PropertyName": "Culture"}}]}, {"Kind": "Components.Bind", "Name": "format-value", "TypeName": "System.String", "Documentation": "Specifies a format to convert the value specified by the '@bind' attribute. The format string can currently only be used with expressions of type <code>DateTime</code>.", "Metadata": {"Common.PropertyName": "Format_value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Common.ClassifyAttributesOnly": "True", "Components.Bind.ValueAttribute": "value", "Components.Bind.ChangeAttribute": "onchange", "Components.Bind.IsInvariantCulture": "False", "Components.Bind.Format": null, "Common.TypeName": "Microsoft.AspNetCore.Components.Web.BindAttributes"}}, {"HashCode": 1864122447, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputCheckbox", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputCheckbox", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.Boolean>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputCheckbox"}}, {"HashCode": 490191036, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputCheckbox", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputCheckbox", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.Boolean>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputCheckbox", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 877686389, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputDate<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputDate", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputDate<TValue>"}}, {"HashCode": 899488481, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputDate<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputDate", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputDate<TValue>", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -543982663, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputNumber", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>"}}, {"HashCode": 184745786, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputNumber", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputNumber<TValue>", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": -796020349, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputSelect", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>"}}, {"HashCode": 646250911, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputSelect", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<TValue>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputSelect<TValue>", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 1614905710, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputText", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputText", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.String>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputText"}}, {"HashCode": 789494904, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputText", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputText", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.String>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputText", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 282414888, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputTextArea", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "InputTextArea", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.String>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputTextArea"}}, {"HashCode": -1742066353, "Kind": "Components.Bind", "Name": "Microsoft.AspNetCore.Components.Forms.InputTextArea", "AssemblyName": "Microsoft.AspNetCore.Components.Web", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "Microsoft.AspNetCore.Components.Forms.InputTextArea", "Attributes": [{"Name": "@bind-Value", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Bind", "Name": "@bind-Value", "TypeName": "Microsoft.AspNetCore.Components.EventCallback<System.String>", "Documentation": "Binds the provided expression to the 'Value' property and a change event delegate to the 'ValueChanged' property of the component.", "Metadata": {"Common.DirectiveAttribute": "True", "Common.PropertyName": "Value"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Bind", "Components.Bind.ValueAttribute": "Value", "Components.Bind.ChangeAttribute": "ValueChanged", "Components.Bind.ExpressionAttribute": "ValueExpression", "Common.TypeName": "Microsoft.AspNetCore.Components.Forms.InputTextArea", "Components.NameMatch": "Components.FullyQualifiedNameMatch"}}, {"HashCode": 93715084, "Kind": "Components.Ref", "Name": "Ref", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Populates the specified field or property with a reference to the element or component.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@ref", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Ref", "Name": "@ref", "TypeName": "System.Object", "Documentation": "Populates the specified field or property with a reference to the element or component.", "Metadata": {"Common.PropertyName": "Ref", "Common.DirectiveAttribute": "True"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Ref", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Ref"}}, {"HashCode": -115576540, "Kind": "Components.Key", "Name": "Key", "AssemblyName": "Microsoft.AspNetCore.Components", "Documentation": "Ensures that the component or element will be preserved across renders if (and only if) the supplied key value matches.", "CaseSensitive": true, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "@key", "Metadata": {"Common.DirectiveAttribute": "True"}}]}], "BoundAttributes": [{"Kind": "Components.Key", "Name": "@key", "TypeName": "System.Object", "Documentation": "Ensures that the component or element will be preserved across renders if (and only if) the supplied key value matches.", "Metadata": {"Common.PropertyName": "Key", "Common.DirectiveAttribute": "True"}}], "Metadata": {"Runtime.Name": "Components.None", "Components.IsSpecialKind": "Components.Key", "Common.ClassifyAttributesOnly": "True", "Common.TypeName": "Microsoft.AspNetCore.Components.Key"}}], "CSharpLanguageVersion": 800}, "RootNamespace": "SerilogWebApp", "Documents": [{"FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\Views\\New\\Index.cshtml", "TargetPath": "Views\\New\\Index.cshtml", "FileKind": "mvc"}, {"FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\Views\\New\\Display.cshtml", "TargetPath": "Views\\New\\Display.cshtml", "FileKind": "mvc"}, {"FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\Views\\Shared\\Error.cshtml", "TargetPath": "Views\\Shared\\Error.cshtml", "FileKind": "mvc"}, {"FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\Views\\Shared\\_Layout.cshtml", "TargetPath": "Views\\Shared\\_Layout.cshtml", "FileKind": "mvc"}, {"FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\Views\\_ViewImports.cshtml", "TargetPath": "Views\\_ViewImports.cshtml", "FileKind": "mvc"}, {"FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\Views\\Home\\Index.cshtml", "TargetPath": "Views\\Home\\Index.cshtml", "FileKind": "mvc"}, {"FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\Views\\_ViewStart.cshtml", "TargetPath": "Views\\_ViewStart.cshtml", "FileKind": "mvc"}, {"FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\Views\\Shared\\_ValidationScriptsPartial.cshtml", "TargetPath": "Views\\Shared\\_ValidationScriptsPartial.cshtml", "FileKind": "mvc"}, {"FilePath": "D:\\SerilogWebApp\\SerilogWebApp\\Views\\Home\\Privacy.cshtml", "TargetPath": "Views\\Home\\Privacy.cshtml", "FileKind": "mvc"}], "SerializationFormat": "0.3"}