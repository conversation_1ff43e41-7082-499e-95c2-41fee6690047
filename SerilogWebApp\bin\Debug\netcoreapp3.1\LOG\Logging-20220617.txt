2022-06-17 15:11:06.694 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2022-06-17 15:11:07.159 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2022-06-17 15:11:07.162 +05:30 [INF] Hosting environment: Development
2022-06-17 15:11:07.173 +05:30 [INF] Content root path: D:\SerilogWebApp\SerilogWebApp
2022-06-17 15:11:07.214 +05:30 [INF] Request starting HTTP/2.0 GET https://localhost:44327/  
2022-06-17 15:11:07.273 +05:30 [INF] Executing endpoint 'SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)'
2022-06-17 15:11:07.338 +05:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller SerilogWebApp.Controllers.HomeController (SerilogWebApp).
2022-06-17 15:11:07.345 +05:30 [ERR] System.Exception: Exception of type 'System.Exception' was thrown.
2022-06-17 15:11:07.349 +05:30 [ERR] [HomeController.cs Index 41]	System.Exception: Exception of type 'System.Exception' was thrown.
2022-06-17 15:11:07.372 +05:30 [INF] Executing ViewResult, running view Index.
2022-06-17 15:11:07.562 +05:30 [INF] Executed ViewResult - view Index executed in 196.041ms.
2022-06-17 15:11:07.569 +05:30 [INF] Executed action SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp) in 227.6862ms
2022-06-17 15:11:07.573 +05:30 [INF] Executed endpoint 'SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)'
2022-06-17 15:11:07.578 +05:30 [INF] HTTP GET / responded 200 in 326.3378 ms
2022-06-17 15:11:07.611 +05:30 [INF] Request finished in 402.6421ms 200 text/html; charset=utf-8
2022-06-17 15:11:07.641 +05:30 [INF] Request starting HTTP/2.0 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js  
2022-06-17 15:11:07.649 +05:30 [INF] Request finished in 8.1595ms 200 application/javascript; charset=utf-8
2022-06-17 15:12:54.912 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2022-06-17 15:12:55.256 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2022-06-17 15:12:55.259 +05:30 [INF] Hosting environment: Development
2022-06-17 15:12:55.262 +05:30 [INF] Content root path: D:\SerilogWebApp\SerilogWebApp
2022-06-17 15:12:55.297 +05:30 [INF] Request starting HTTP/2.0 GET https://localhost:44327/  
2022-06-17 15:12:55.352 +05:30 [INF] Executing endpoint 'SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)'
2022-06-17 15:12:55.400 +05:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller SerilogWebApp.Controllers.HomeController (SerilogWebApp).
2022-06-17 15:12:55.407 +05:30 [ERR] System.Exception: Exception of type 'System.Exception' was thrown.
2022-06-17 15:12:55.410 +05:30 [ERR] [HomeController.cs Index 41]	System.Exception: Exception of type 'System.Exception' was thrown.
2022-06-17 15:12:55.439 +05:30 [INF] Executing ViewResult, running view Index.
2022-06-17 15:12:55.656 +05:30 [INF] Executed ViewResult - view Index executed in 220.9104ms.
2022-06-17 15:12:55.664 +05:30 [INF] Executed action SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp) in 261.1122ms
2022-06-17 15:12:55.667 +05:30 [INF] Executed endpoint 'SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)'
2022-06-17 15:12:55.672 +05:30 [INF] HTTP GET / responded 200 in 337.8450 ms
2022-06-17 15:12:55.710 +05:30 [INF] Request finished in 417.6804ms 200 text/html; charset=utf-8
2022-06-17 15:12:55.743 +05:30 [INF] Request starting HTTP/2.0 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js  
2022-06-17 15:12:55.753 +05:30 [INF] Request finished in 9.7315ms 200 application/javascript; charset=utf-8
