{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "LogFileInfo": {
    "FilePath": "D:\\Serilog",
    "LogFileName": "Custom.log",
    "Active": "NO"
  }
  //"Serilog": {
  //  "Using": [ "Serilog.Sinks.File", "Serilog.Enrichers.Environment" ],
  //  //"MinimumLevel": "Verbose",
  //  "MinimulLevel": {
  //    "Default": "Warning",
  //    "Override": {
  //      "Microsoft": "Warning",
  //      "System": "Warning"
  //    }
  //  },
  //  "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadName", "WithProcessId", "WithThreadId", "WithExceptionDetails", "WithStackTrace", "ElapsedMilliseconds" ],
  //  "Properties": {
  //    "ApplicationName": "SerilogApplication "
  //    //"HostName": "$HOSTNAME" 
  //  },
  //  "WriteTo": [

  //    {
  //      "Name": "File",
  //      "Args": {
  //        "path": "D:\\Serilogs\\log-.log",
  //        "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3} {Host} {ApplicationName} PID{ProcessId} {ElapsedMilliseconds}  {Message:l} {NewLine}{Exception}",
  //        "rollingInterval": "Day"
  //      }
  //    }
  //  ]
  //}

}
