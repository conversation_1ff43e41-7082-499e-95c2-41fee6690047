{"@t":"2025-07-01T12:18:57.4234012Z","@m":"User profile is available. Using '\"C:\\Users\\<USER>\\AppData\\Local\\ASP.NET\\DataProtection-Keys\"' as key repository and Windows DPAPI to encrypt keys at rest.","@i":"7ac5e29c","FullName":"C:\\Users\\<USER>\\AppData\\Local\\ASP.NET\\DataProtection-Keys","EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0001"}
{"@t":"2025-07-01T12:18:58.3005195Z","@m":"Application started. Press Ctrl+C to shut down.","@i":"dcaefe54","SourceContext":"Microsoft.Hosting.Lifetime","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0001"}
{"@t":"2025-07-01T12:18:58.3049953Z","@m":"Hosting environment: \"Development\"","@i":"c3307c92","envName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0001"}
{"@t":"2025-07-01T12:18:58.3051850Z","@m":"Content root path: \"C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp\"","@i":"b5d60022","contentRoot":"C:\\Users\\<USER>\\OneDrive - mobileaspectshealth\\Backup\\IRISECUREBLOOD\\Serilog\\SerilogWebAppV1.0.0.0\\SerilogWebApp","SourceContext":"Microsoft.Hosting.Lifetime","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0001"}
{"@t":"2025-07-01T12:18:58.3141560Z","@m":"Request starting HTTP/2 GET https://localhost:44327/ - -","@i":"ca22a1cb","Protocol":"HTTP/2","Method":"GET","ContentType":null,"ContentLength":null,"Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/","QueryString":"","HostingRequestStartingLog":"Request starting HTTP/2 GET https://localhost:44327/ - -","EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","ThreadID":"0009"}
{"@t":"2025-07-01T12:18:58.8450975Z","@m":"Executing endpoint '\"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)\"'","@i":"500cc934","EndpointName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:58.9286681Z","@m":"Route matched with \"{action = \\\"Index\\\", controller = \\\"Home\\\"}\". Executing controller action with signature \"Microsoft.AspNetCore.Mvc.IActionResult Index()\" on controller \"SerilogWebApp.Controllers.HomeController\" (\"SerilogWebApp\").","@i":"122b2fdf","RouteData":"{action = \"Index\", controller = \"Home\"}","MethodInfo":"Microsoft.AspNetCore.Mvc.IActionResult Index()","Controller":"SerilogWebApp.Controllers.HomeController","AssemblyName":"SerilogWebApp","EventId":{"Id":3,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"c0fe1760-d8db-4545-bffc-3f4981e3fc3b","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:58.9355719Z","@m":"HomeController.cs Index 42 Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","@i":"52f3948c","ApplicationName":"SerilogWebApp","ActionId":"c0fe1760-d8db-4545-bffc-3f4981e3fc3b","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:58.9404391Z","@m":"HomeController.cs Index 43 System.Exception: Exception of type 'System.Exception' was thrown.","@i":"e82d03b1","@l":"Error","ApplicationName":"SerilogWebApp","ActionId":"c0fe1760-d8db-4545-bffc-3f4981e3fc3b","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:58.9410100Z","@m":"HomeController.cs Index 44 This is an information","@i":"bd205256","ApplicationName":"SerilogWebApp","ActionId":"c0fe1760-d8db-4545-bffc-3f4981e3fc3b","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:58.9613285Z","@m":"Executing ViewResult, running view \"Index\".","@i":"c83d0e25","ViewName":"Index","EventId":{"Id":1,"Name":"ViewResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor","ActionId":"c0fe1760-d8db-4545-bffc-3f4981e3fc3b","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:59.3178866Z","@m":"Executed ViewResult - view \"Index\" executed in 361.2599ms.","@i":"f66409e9","ViewName":"Index","ElapsedMilliseconds":361.2599,"EventId":{"Id":4,"Name":"ViewResultExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor","ActionId":"c0fe1760-d8db-4545-bffc-3f4981e3fc3b","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:59.3253037Z","@m":"Executed action \"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)\" in 395.1339ms","@i":"afa2e885","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","ElapsedMilliseconds":395.1339,"EventId":{"Id":2,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:59.3258355Z","@m":"Executed endpoint '\"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)\"'","@i":"99874f2b","EndpointName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:59.3480047Z","@m":"HTTP \"GET\" \"/\" responded 200 in 531.4693 ms","@i":"62d0885c","ActionName":"SerilogWebApp.Controllers.HomeController.Index (SerilogWebApp)","ActionId":"c0fe1760-d8db-4545-bffc-3f4981e3fc3b","RequestMethod":"GET","RequestPath":"/","StatusCode":200,"Elapsed":531.4693,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","ApplicationName":"SerilogWebApp","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","Host":"MAILPTP93","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:59.3930443Z","@m":"Request finished HTTP/2 GET https://localhost:44327/ - - - 200 - text/html;+charset=utf-8 1086.7638ms","@i":"791a596a","ElapsedMilliseconds":1086.7638,"StatusCode":200,"ContentType":"text/html; charset=utf-8","ContentLength":null,"Protocol":"HTTP/2","Method":"GET","Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/","QueryString":"","HostingRequestFinishedLog":"Request finished HTTP/2 GET https://localhost:44327/ - - - 200 - text/html;+charset=utf-8 1086.7638ms","EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"4000019d-0006-f900-b63f-84710c7967bb","RequestPath":"/","ApplicationName":"SerilogWebApp","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:59.4822854Z","@m":"Request starting HTTP/2 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js - -","@i":"ca22a1cb","Protocol":"HTTP/2","Method":"GET","ContentType":null,"ContentLength":null,"Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/_framework/aspnetcore-browser-refresh.js","QueryString":"","HostingRequestStartingLog":"Request starting HTTP/2 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js - -","EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"400000d7-0004-f700-b63f-84710c7967bb","RequestPath":"/_framework/aspnetcore-browser-refresh.js","ApplicationName":"SerilogWebApp","ThreadID":"0014"}
{"@t":"2025-07-01T12:18:59.5011400Z","@m":"Request starting HTTP/2 GET https://localhost:44327/_vs/browserLink - -","@i":"ca22a1cb","Protocol":"HTTP/2","Method":"GET","ContentType":null,"ContentLength":null,"Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/_vs/browserLink","QueryString":"","HostingRequestStartingLog":"Request starting HTTP/2 GET https://localhost:44327/_vs/browserLink - -","EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"40000152-0004-f900-b63f-84710c7967bb","RequestPath":"/_vs/browserLink","ApplicationName":"SerilogWebApp","ThreadID":"0004"}
{"@t":"2025-07-01T12:18:59.5197612Z","@m":"Request finished HTTP/2 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js - - - 200 12358 application/javascript;+charset=utf-8 33.4386ms","@i":"791a596a","ElapsedMilliseconds":33.4386,"StatusCode":200,"ContentType":"application/javascript; charset=utf-8","ContentLength":12358,"Protocol":"HTTP/2","Method":"GET","Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/_framework/aspnetcore-browser-refresh.js","QueryString":"","HostingRequestFinishedLog":"Request finished HTTP/2 GET https://localhost:44327/_framework/aspnetcore-browser-refresh.js - - - 200 12358 application/javascript;+charset=utf-8 33.4386ms","EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"400000d7-0004-f700-b63f-84710c7967bb","RequestPath":"/_framework/aspnetcore-browser-refresh.js","ApplicationName":"SerilogWebApp","ThreadID":"0014"}
{"@t":"2025-07-01T12:18:59.7532663Z","@m":"Request finished HTTP/2 GET https://localhost:44327/_vs/browserLink - - - 200 - text/javascript;+charset=UTF-8 252.0917ms","@i":"791a596a","ElapsedMilliseconds":252.0917,"StatusCode":200,"ContentType":"text/javascript; charset=UTF-8","ContentLength":null,"Protocol":"HTTP/2","Method":"GET","Scheme":"https","Host":"localhost:44327","PathBase":"","Path":"/_vs/browserLink","QueryString":"","HostingRequestFinishedLog":"Request finished HTTP/2 GET https://localhost:44327/_vs/browserLink - - - 200 - text/javascript;+charset=UTF-8 252.0917ms","EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"40000152-0004-f900-b63f-84710c7967bb","RequestPath":"/_vs/browserLink","ApplicationName":"SerilogWebApp","ThreadID":"0009"}
