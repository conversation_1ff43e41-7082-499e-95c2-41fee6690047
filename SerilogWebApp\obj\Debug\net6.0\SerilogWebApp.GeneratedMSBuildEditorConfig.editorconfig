is_global = true
build_property.TargetFramework = net6.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = SerilogWebApp
build_property.RootNamespace = SerilogWebApp
build_property.ProjectDir = C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 6.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 6.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/OneDrive - mobileaspectshealth/Backup/IRISECUREBLOOD/Serilog/SerilogWebAppV1.0.0.0/SerilogWebApp/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - mobileaspectshealth/Backup/IRISECUREBLOOD/Serilog/SerilogWebAppV1.0.0.0/SerilogWebApp/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - mobileaspectshealth/Backup/IRISECUREBLOOD/Serilog/SerilogWebAppV1.0.0.0/SerilogWebApp/Views/New/Display.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcTmV3XERpc3BsYXkuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - mobileaspectshealth/Backup/IRISECUREBLOOD/Serilog/SerilogWebAppV1.0.0.0/SerilogWebApp/Views/New/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcTmV3XEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - mobileaspectshealth/Backup/IRISECUREBLOOD/Serilog/SerilogWebAppV1.0.0.0/SerilogWebApp/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - mobileaspectshealth/Backup/IRISECUREBLOOD/Serilog/SerilogWebAppV1.0.0.0/SerilogWebApp/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - mobileaspectshealth/Backup/IRISECUREBLOOD/Serilog/SerilogWebAppV1.0.0.0/SerilogWebApp/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - mobileaspectshealth/Backup/IRISECUREBLOOD/Serilog/SerilogWebAppV1.0.0.0/SerilogWebApp/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - mobileaspectshealth/Backup/IRISECUREBLOOD/Serilog/SerilogWebAppV1.0.0.0/SerilogWebApp/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
