#pragma checksum "D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\Views\New\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "11d8291a7dda9e032bea60ad958475f9b05109f2a7730ce3c483555999bc06f3"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCore.Views_New_Index), @"mvc.1.0.view", @"/Views/New/Index.cshtml")]
namespace AspNetCore
{
    #line hidden
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\Views\_ViewImports.cshtml"
using SerilogWebApp;

#line default
#line hidden
#nullable disable
#nullable restore
#line 2 "D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\Views\_ViewImports.cshtml"
using SerilogWebApp.Models;

#line default
#line hidden
#nullable disable
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA256", @"11d8291a7dda9e032bea60ad958475f9b05109f2a7730ce3c483555999bc06f3", @"/Views/New/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA256", @"8847710bc15420c8c725273a8e4d3cacb6779983778327241693dfb50595cf3d", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    public class Views_New_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("<p> this is the index page of new controller.</p>\r\n<a");
            BeginWriteAttribute("href", " href=\"", 53, "\"", 86, 1);
#nullable restore
#line 2 "D:\IRISECUREBLOOD\Serilog\SerilogWebApp_bkup\SerilogWebApp\Views\New\Index.cshtml"
WriteAttributeValue("", 60, Url.Action("Index","New"), 60, 26, false);

#line default
#line hidden
#nullable disable
            EndWriteAttribute();
            WriteLiteral(">Link 1</a>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
