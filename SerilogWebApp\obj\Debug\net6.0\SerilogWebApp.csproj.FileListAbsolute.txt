D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\appsettings.Development.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\appsettings.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.staticwebassets.runtime.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.exe
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.deps.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.runtimeconfig.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Microsoft.Extensions.DependencyModel.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.AspNetCore.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Enrichers.Environment.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Enrichers.Process.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Enrichers.Thread.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Extensions.Hosting.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Extensions.Logging.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Formatting.Compact.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Formatting.Syslog.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Settings.Configuration.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Sinks.Console.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Sinks.Debug.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Sinks.File.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogTimings.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Logger.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Logger.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.csproj.AssemblyReference.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.GeneratedMSBuildEditorConfig.editorconfig
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.AssemblyInfoInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.AssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.csproj.CoreCompileInputs.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.RazorAssemblyInfo.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.RazorAssemblyInfo.cs
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets.build.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets.development.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets\msbuild.SerilogWebApp.Microsoft.AspNetCore.StaticWebAssets.props
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets\msbuild.build.SerilogWebApp.props
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets\msbuild.buildMultiTargeting.SerilogWebApp.props
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets\msbuild.buildTransitive.SerilogWebApp.props
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets.pack.json
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\scopedcss\bundle\SerilogWebApp.styles.css
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.csproj.CopyComplete
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\refint\SerilogWebApp.dll
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.pdb
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.genruntimeconfig.cache
D:\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\ref\SerilogWebApp.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\appsettings.Development.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\appsettings.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.staticwebassets.runtime.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.staticwebassets.endpoints.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.exe
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.deps.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.runtimeconfig.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogWebApp.pdb
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.AspNetCore.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Enrichers.Environment.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Enrichers.Process.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Enrichers.Thread.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Formatting.Compact.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Formatting.Syslog.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Sinks.File.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\SerilogTimings.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Logger.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Logger.pdb
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.csproj.AssemblyReference.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\rpswa.dswa.cache.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.AssemblyInfoInputs.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.AssemblyInfo.cs
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.csproj.CoreCompileInputs.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.RazorAssemblyInfo.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.RazorAssemblyInfo.cs
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\rjimswa.dswa.cache.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\rjsmrazor.dswa.cache.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\rjsmcshtml.dswa.cache.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\scopedcss\bundle\SerilogWebApp.styles.css
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets.build.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets.build.json.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets.development.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogW.26AE0CA8.Up2Date
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\refint\SerilogWebApp.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.pdb
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\SerilogWebApp.genruntimeconfig.cache
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\obj\Debug\net6.0\ref\SerilogWebApp.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Logs\ApplicationLog20250701.json
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Sinks.Datadog.Logs.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Serilog.Sinks.PeriodicBatching.dll
C:\Users\<USER>\OneDrive - mobileaspectshealth\Backup\IRISECUREBLOOD\Serilog\SerilogWebAppV1.0.0.0\SerilogWebApp\bin\Debug\net6.0\Logs\WebLog20250701.json
